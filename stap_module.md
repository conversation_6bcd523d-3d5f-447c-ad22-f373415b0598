Staff Module


Core Purpose: To manage all non-student user accounts, assign roles, and precisely control access permissions, empowering the admin to securely delegate responsibilities.

Navigation Trigger:
Clicking Staff (fa-users) in the Side Navigation.

[Screen: User Management - Staff List View]
Purpose: A central directory for managing teachers, bursars, and other administrative staff.
Layout: A standard full-screen table view. The complexity lies within the edit/add modal.
UI Elements & Content:
[Element: Page Header]
Page Title: "Staff Management"
Action: [Primary Button: fa-user-plus "Add New Staff"] -> Triggers [Modal: Add/Edit Staff].
[Element: Data Table - Staff List]
Columns: Staff Photo (User.avatar), Name, Role (e.g., Teacher, Bursar), Email, Phone, Status (Active/Inactive toggle).
Row Actions (on hover):
[Icon Button: fa-pencil-alt "Edit"] -> Triggers [Modal: Add/Edit Staff] pre-filled.
[Icon Button: fa-key "Reset Password"] -> Triggers a confirmation to send a password reset link.
[Modal: Add/Edit Staff]
Purpose: A powerful and intuitive interface for creating staff accounts and defining their exact permissions within the app.
Title: "Add New Staff Member" or "Edit Profile: [Staff Name]"
Layout: A multi-step or tabbed modal to prevent overwhelming the user.
Step 1: Personal Information
Form Fields: Full Name, Email (used for login), Phone Number, Upload Avatar.
Step 2: Role & Permissions
Purpose: This is the core of role delegation.
[Element: Role Template Dropdown]
Label: "Assign a Role Template"
Options: "Teacher," "Bursar," "Dean of Studies," "Custom."
Behavior: Selecting a template (e.g., "Teacher") automatically checks the relevant default permissions in the list below. Selecting "Custom" leaves them all blank.
[Element: Granular Permissions Checklist]
Layout: A tree-like checklist, grouped by the sidebar modules for intuitive navigation.
Example Groups & Permissions:
Group: Students
[ ] View All Students
[ ] Add/Edit/Delete Students
[ ] Generate ID Cards & Report Cards
Group: Academic Records
[ ] View Grades (for Assigned Classes only)
[ ] Enter/Edit Grades (for Assigned Classes only)
[ ] View All School Grades (Dean/Admin permission)
[ ] Take Attendance (for Assigned Classes only)
Group: Financials
[ ] View Student Fee Balances
[ ] Record Fee Payments
[ ] Manage School Credit Balance
Group: Staff
[ ] View Staff List
[ ] Add/Edit/Delete Staff (Admin-only permission)
Step 3: Assignments (Conditional)
Visibility: This step only appears if the "Teacher" role template is selected or if any "Assigned Classes only" permissions are checked.
Title: "Assign Teaching Schedule"
UI: An intuitive interface to link the teacher to ClassSchedule slots. A table with columns "Class," "Subject," "Periods," and an "Add" button to create new assignments for this teacher.
[Element: Modal Footer]
Action: [Primary Button: "Save Staff Member"] -> Creates/Updates the User record and their associated permissions.
