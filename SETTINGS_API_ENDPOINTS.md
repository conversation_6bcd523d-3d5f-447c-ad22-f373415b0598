# Settings API Endpoints Documentation

This document outlines the API endpoints needed to support the complete settings functionality.

## Credit Settings Endpoints

### GET /api/system/credit-settings
Get current credit settings

**Response:**
```json
{
  "pricePerCredit": 10.00,
  "paymentGateway": "stripe",
  "latePaymentFee": 5.00,
  "paymentDuePeriod": 30
}
```

### PUT /api/system/credit-settings
Update credit settings

**Request Body:**
```json
{
  "pricePerCredit": 10.00,
  "paymentGateway": "stripe",
  "latePaymentFee": 5.00,
  "paymentDuePeriod": 30
}
```

**Response:**
```json
{
  "pricePerCredit": 10.00,
  "paymentGateway": "stripe",
  "latePaymentFee": 5.00,
  "paymentDuePeriod": 30
}
```

## Enhanced System Settings

### Update existing GET /api/system/settings
Add `maintenanceMessage` field to the existing response:

**Enhanced Response:**
```json
{
  "platformName": "Scholarify",
  "platformDescription": "Comprehensive School Management System",
  "supportEmail": "<EMAIL>",
  "maintenanceMode": false,
  "maintenanceMessage": "We are currently performing scheduled maintenance. Please check back soon.",
  "allowNewRegistrations": true,
  "maxSchoolsPerSubscription": 5,
  "defaultSubscriptionDuration": 12,
  "emailNotifications": true,
  "smsNotifications": true,
  "systemBackupFrequency": "daily",
  "dataRetentionPeriod": 365
}
```

### Update existing PUT /api/system/settings
Accept `maintenanceMessage` field in the request body.

## Security Settings Endpoints (Enhanced)

### GET /api/system/security-settings
**Response:**
```json
{
  "passwordMinLength": 8,
  "requireSpecialCharacters": true,
  "sessionTimeout": 30,
  "maxLoginAttempts": 5,
  "twoFactorRequired": false,
  "ipWhitelist": []
}
```

### PUT /api/system/security-settings
**Request Body:** Same as GET response structure.

## Maintenance Mode Implementation

The maintenance mode functionality includes:

1. **Frontend checks** via `MaintenanceModeProvider`
2. **Middleware support** for route-level maintenance mode
3. **Dynamic maintenance messages** configurable through settings
4. **Admin bypass** - Super admins can access the system during maintenance

### Environment Variables (Optional)
```env
MAINTENANCE_MODE=false
```

## Current Status

✅ **Implemented in Frontend:**
- Credit settings UI and form handling
- Maintenance mode component and provider
- Enhanced system settings with maintenance message
- Error handling with graceful fallbacks

⏳ **Needs Backend Implementation:**
- Credit settings API endpoints
- Enhanced system settings with maintenanceMessage
- Security settings API endpoints (if not already implemented)

## Testing

Until the backend endpoints are implemented, the frontend will:
- Use default values for missing endpoints
- Show development warnings in console
- Display a development notice to users
- Allow users to interact with the UI (though changes won't persist)

This approach ensures the frontend is ready for production once the backend endpoints are implemented.