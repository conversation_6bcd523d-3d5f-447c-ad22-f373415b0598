// // controllers/userController.js
const firebase = require('../utils/firebase')
const bcrypt = require('bcryptjs');
const User = require('../models/User');  // Assuming you have a User model
const Student = require('../models/Student'); // Assuming you have a Student model
const crypto = require('crypto');
const sendEmail = require('../utils/sendEmail');
const sendSMS = require('../utils/sendSMS'); // Assuming you have a sendSMS utility
const { getMonthDateRange } = require('../utils/DateRange');
const admin = require('firebase-admin');

const testUserResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is user' });
};
// // Get all users
const getAllUsers = async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getParents = async (req, res) => {
  try {
    const parents = await User.find({ role: 'parent' });
    res.json(parents);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
// Assuming you're using Express.js and Mongoose

const getParentsByPage = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1; // Get page number from query, default to 1
    const limit = parseInt(req.query.limit) || 20; // Get limit from query, default to 20 parents per page

    const skip = (page - 1) * limit; // Calculate how many documents to skip

    // Get total count of parents (for client to know if there's more data)
    const totalParents = await User.countDocuments({ role: 'parent' });

    // Fetch parents for the current page, sorted by creation date (or any relevant field)
    const parents = await User.find({ role: 'parent' })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 }); // Assuming you have a 'createdAt' field, or use another relevant field

    res.json({
      parents,
      currentPage: page,
      totalPages: Math.ceil(totalParents / limit),
      totalParents,
      hasMore: (page * limit) < totalParents // Simple check if there are more pages
    });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Make sure this function is exported and used in your router, e.g.:
// router.get('/parents', getParents);


const registerUser = async (req, res) => {
  const { email, password, role, name, address, school_ids, phone } = req.body;

  try {
    // Validate that the required fields are provided
    if (!email || !password || !name || !role) {
      return res.status(400).json({ message: 'Email, password, name and role are required' });
    }

    // Check if a user with the same email or firebaseUid already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({ message: 'User with this email already exists' });
    }

    // Register user with Firebase
    const userRecord = await firebase.auth().createUser({
      email,
      password,
    });

    // Hash password before saving to DB
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate user_id based on role
    let userIdPrefix = '';
    switch (role) {
      case 'teacher':
        userIdPrefix = 'TR'; // For teacher, user ID starts with 'TR'
        break;
      case 'admin':
        userIdPrefix = 'AD'; // For admin, user ID starts with 'AD'
        break;
      case 'super':
        userIdPrefix = 'SP'; // For super, user ID starts with 'SP'
        break;
      default:
        userIdPrefix = 'PR'; // For parent, user ID starts with 'PR'
        break;
    }

    const randomNumber = Math.floor(Math.random() * 25000000);  // Random number between 0 and 24,999,999
    const userId = `${userIdPrefix}-${randomNumber.toString().padStart(7, '0')}`;  // Format to always have 7 digits

    // Create user in MongoDB
    const user = new User({
      user_id: userId, // The user_id is now generated based on the role
      firebaseUid: userRecord.uid,
      name,
      role,
      phone,
      email,
      password: hashedPassword,
      address,
      school_ids: school_ids || [], // Default to an empty array if no school_ids are provided
    });

    await user.save();
    return res.status(201).json({ message: 'User registered successfully', user });
  } catch (error) {
    console.error('Error during registration:', error);
    return res.status(500).json({ message: 'Registration failed', error: error.message });
  }
}


//
const createUser = async (req, res) => {
  try {
    const newUser = new User(req.body);
    await newUser.save();
    res.status(201).json(newUser);
  } catch (err) {
    const customMessage = "Failed to create a new user.";
    res.status(400).json({ customMessage, message: err.message });
  }
};

// // Get a user by ID
const getUserById = async (req, res) => {
  try {
    const userId = req.params.id;  // Get the user_id from the URL parameter

    // Find the user by user_id
    const user = await User.findOne({ user_id: userId });

    if (!user) {
      return res.status(404).json({ message: 'User not found' }); // If user not found
    }

    res.json(user); // Return the user data
  } catch (err) {
    res.status(500).json({ message: err.message }); // Handle server errors
  }
};

const getUserBy_id = async (req, res) => {
  try {
    const _id = req.params.id; // Get the MongoDB _id from the URL

    // Find the user by MongoDB's _id
    const user = await User.findById(_id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user); // Return the user data
  } catch (err) {
    res.status(500).json({ message: err.message }); // Handle server errors
  }
};

const getUserByEmail = async (req, res) => {
  try {
    const email = req.params.email;  // Get the email from the URL parameter

    // Find the user by email
    const user = await User.findOne({ email: email });

    if (!user) {
      return res.status(404).json({ message: 'User not found' }); // If user not found
    }

    res.json(user); // Return the user data
  } catch (err) {
    res.status(500).json({ message: err.message }); // Handle server errors
  }
};

// // Update user by ID
const updateUserById = async (req, res) => {
  try {
    const userId = req.params.id; // Get the user_id from the URL parameter (assuming this is the Firebase UID)
    const updateData = req.body;   // Get the updated data from the request body

    let updatedUser;

    // Check if a password update is requested
    if (updateData.password) {
      // 1. Update password in Firebase Authentication
      try {
        await admin.auth().updateUser(userId, {
          password: updateData.password,
        });
        console.log(`Firebase password updated successfully for user: ${userId}`);

        // IMPORTANT: Remove password from updateData before saving to MongoDB
        // We generally do NOT store passwords in our MongoDB if Firebase handles authentication.
        // If you absolutely need to store a password hash for some reason (e.g., legacy system),
        // ensure you hash it here before saving to MongoDB, but it's not recommended.
        delete updateData.password;

      } catch (firebaseError) {
        console.error("Firebase password update failed:", firebaseError);
        // Depending on your error handling, you might want to return an error here
        // if Firebase update is critical.
        if (firebaseError.code === 'auth/user-not-found') {
             return res.status(404).send({ message: 'User not found in Firebase Authentication' });
        }
        return res.status(400).send({ message: 'Failed to update Firebase password', error: firebaseError.message });
      }
    }

    // 2. Update user data in MongoDB (excluding password if it was handled by Firebase)
    if (Object.keys(updateData).length > 0) { // Only update if there's still data after password removal
        updatedUser = await User.findOneAndUpdate(
            { user_id: userId },   // Search by user_id (Firebase UID)
            updateData,            // Update data (without password if it was present)
            { new: true }          // Return the updated document
        );

        if (!updatedUser) {
            return res.status(404).send({ message: 'User not found in database' }); // Handle user not found in MongoDB
        }
    } else if (!updatedUser && updateData.password) {
        // If only password was updated and no other data, and updatedUser wasn't set by MongoDB
        // We still need to return a success response, possibly by fetching the user.
        updatedUser = await User.findOne({ user_id: userId });
        if (!updatedUser) {
             return res.status(404).send({ message: 'User not found after password update (MongoDB lookup)' });
        }
    }

    res.status(200).send(updatedUser || { message: 'User password updated successfully (no other data changed)' }); // Return the updated user or a success message
  } catch (err) {
    console.error("Error in updateUserById:", err);
    res.status(400).send({ message: 'Failed to update user', error: err.message }); // Handle errors
  }
};

// // Delete user by ID
const deleteUserById = async (req, res) => {
  try {
    const userId = req.params.id; // Get the user_id from the URL parameter

    // Find the user by user_id and delete it
    const deletedUser = await User.findOneAndDelete({ user_id: userId });

    if (!deletedUser) {
      return res.status(404).json({ message: 'User not found' }); // If user not found
    }

    res.json({ message: 'User deleted successfully' }); // If user is deleted
  } catch (err) {
    res.status(500).json({ message: err.message }); // Handle server errors
  }
};

// Delete multiple user records by IDs
const deleteMultipleUsers = async (req, res) => {
  const { ids } = req.body; // Expecting an array of user IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete user records where _id is in the provided array of IDs
    const result = await User.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No user records found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} user records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL user records
const deleteAllUsers = async (req, res) => {
  try {
    // First, count how many users exist
    const userCount = await User.countDocuments();

    if (userCount === 0) {
      return res.status(404).json({ message: 'No users found to delete' });
    }

    // Delete all user records
    const result = await User.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} user records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Search users by name, email, or phone
const searchUsers = async (req, res) => {
  const { query } = req.query;

  if (!query) {
    return res.status(400).json({ message: "Search query is required" });
  }

  try {
    const users = await User.find({
      $or: [
        { name: { $regex: query, $options: "i" } },  // case-insensitive
        { email: { $regex: query, $options: "i" } },
        { phone: { $regex: query, $options: "i" } }
      ]
    });

    res.json(users);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const generateRandomPassword = (length = 10) => {
  return crypto.randomBytes(length).toString('hex').slice(0, length);
};

const registerParent = async (req, res) => {
  let { name, phone, email, address, school_ids = [], student_ids = [] } = req.body;

  try {
    if ((!email && !phone) || !name) {
      return res.status(400).json({ message: 'Name and either email or phone are required.' });
    }

    const isValidPhone = /^\+[1-9]\d{1,14}$/.test(phone);
    if (phone && !isValidPhone) {
      return res.status(400).json({ message: 'Invalid phone number format (must be in E.164 format)' });
    }

    // Ensure uniqueness and normalize IDs to strings
    const uniqueSchoolIds = Array.from(new Set(school_ids.map(id => id.toString())));
    const uniqueStudentIds = Array.from(new Set(student_ids.map(id => id.toString())));

    // Check for existing user
    const existingUser = await User.findOne({
      $or: [
        email ? { email } : null,
        phone ? { phone } : null,
      ].filter(Boolean),
    });

    if (existingUser) {
      const updatedSchoolIds = Array.from(new Set([
        ...existingUser.school_ids.map(id => id.toString()),
        ...uniqueSchoolIds,
      ]));

      const updatedStudentIds = Array.from(new Set([
        ...existingUser.student_ids.map(id => id.toString()),
        ...uniqueStudentIds,
      ]));

      existingUser.name = name;
      existingUser.address = address || existingUser.address;
      existingUser.school_ids = updatedSchoolIds;
      existingUser.student_ids = updatedStudentIds;

      await existingUser.save();

      await Promise.all(
        uniqueStudentIds.map(async (_id) => {
          await Student.findByIdAndUpdate(
            _id,
            { $addToSet: { guardian_id: existingUser._id } },
            { new: true }
          );
        })
      );

      return res.status(200).json({
        message: 'Existing parent updated with new schools or students',
        user: existingUser,
      });
    }

    // Create new parent
    const plainPassword = generateRandomPassword(10);
    const hashedPassword = await bcrypt.hash(plainPassword, 10);

    const firebaseUser = await firebase.auth().createUser({
      ...(email ? { email } : {}),
      ...(phone ? { phoneNumber: phone } : {}),
      password: plainPassword,
    });

    const randomNumber = Math.floor(Math.random() * 25000000);
    const userId = `PR-${randomNumber.toString().padStart(7, '0')}`;

    const user = new User({
      user_id: userId,
      firebaseUid: firebaseUser.uid,
      name,
      role: 'parent',
      phone: phone || null,
      email: email || null,
      password: hashedPassword,
      address: address || '',
      school_ids: uniqueSchoolIds,
      student_ids: uniqueStudentIds,
    });

    await user.save();

    await Promise.all(
      uniqueStudentIds.map(async (_id) => {
        await Student.findByIdAndUpdate(
          _id,
          { $addToSet: { guardian_id: user._id } },
          { new: true }
        );
      })
    );
    // 🧠 Fetch student names for email
    let studentNames = [];
    if (uniqueStudentIds.length > 0) {
      const students = await Student.find({ _id: { $in: uniqueStudentIds } }, 'name');
      studentNames = students.map(student => student.name);
    }

    const studentList = studentNames.length
      ? `<ul>${studentNames.map(name => `<li>${name}</li>`).join('')}</ul>`
      : '<p>No students associated.</p>';

    const message = `Welcome to Scholarify! Your login details:\nEmail/Phone: ${email || phone}\nPassword: ${plainPassword}`;
    // Send SMS if phone is available
    // if (phone) {
    //   try {
    //     await sendSMS(
    //       phone,
    //       message,
    //     );
    //   } catch (err) {
    //     console.error("Failed to send SMS:", err);
    //   }
    // }
    // Send Email (optional: SMS block can be added back)
    if (email) {
      try {
        await sendEmail({
          to: email,
          subject: 'Welcome to Scholarify',
          html: `
            <h2>Welcome to Scholarify!</h2>
            <p>Your login credentials:</p>
            <ul>
              <li><strong>Email/Phone:</strong> ${email || phone}</li>
              <li><strong>Password:</strong> ${plainPassword}</li>
            </ul>
            <p><strong>Linked Student(s):</strong></p>
            ${studentList}
            <p>Please log in and change your password after first use.</p>
          `,
        });
      } catch (err) {
        console.error("Failed to send email:", err);
      }
    }

    return res.status(201).json({
      message: 'Parent registered successfully',
      // user,
      // generatedPassword: plainPassword,
    });

  } catch (error) {
    console.error('Register parent error:', error);
    return res.status(500).json({ message: 'Failed to register parent', error: error.message });
  }
};

const resetParentPassword = async (req, res) => {
  const { email, phone } = req.body;

  try {
    if (!email && !phone) {
      return res.status(400).json({ message: 'Email or phone is required to reset password.' });
    }

    const user = await User.findOne({
      $or: [
        email ? { email } : null,
        phone ? { phone } : null,
      ].filter(Boolean),
      role: 'parent',
    });

    if (!user) {
      return res.status(404).json({ message: 'Parent not found.' });
    }

    const newPassword = generateRandomPassword(10);
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    user.password = hashedPassword;
    await user.save();

    // 🔐 Reset Firebase password
    await firebase.auth().updateUser(user.firebaseUid, {
      password: newPassword,
    });

    // 🧠 Fetch student names
    let studentNames = [];
    if (user.student_ids && user.student_ids.length > 0) {
      const students = await Student.find(
        { _id: { $in: user.student_ids } },
        'name'
      );
      studentNames = students.map((s) => s.name);
    }

    const studentList = studentNames.length
      ? `<ul>${studentNames.map(name => `<li>${name}</li>`).join('')}</ul>`
      : '<p>No students associated.</p>';

    const message = `Welcome to Scholarify! Your login details:\nEmail/Phone: ${email || phone}\nPassword: ${newPassword}`;

    // Send SMS if phone is available
    // if (phone) {
    //   try {
    //     await sendSMS(
    //       phone,
    //       message,
    //     );
    //   } catch (err) {
    //     console.error("Failed to send SMS:", err);
    //   }
    // }
    // Send Email (optional: SMS block can be added back)

    // 📧 Send email
    if (user.email) {
      try {
        await sendEmail({
          to: user.email,
          subject: 'Your Scholarify Password Has Been Reset',
          html: `
            <h2>Password Reset Notice</h2>
            <p>Your Scholarify password has been successfully reset. Here are your new login credentials:</p>
            <ul>
              <li><strong>Email/Phone:</strong> ${user.email || user.phone}</li>
              <li><strong>New Password:</strong> ${newPassword}</li>
            </ul>
            <p><strong>Linked Student(s):</strong></p>
            ${studentList}
            <p>Please log in and change your password after your next login.</p>
          `,
        });
      } catch (err) {
        console.error('Failed to send reset email:', err);
      }
    }

    return res.status(200).json({
      message: 'Password reset successfully. Please check your email.',
    });
  } catch (error) {
    console.error('Reset parent password error:', error);
    return res.status(500).json({ message: 'Failed to reset password', error: error.message });
  }
};


// Get total number of schools
const getTotalUsers = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    res.status(200).json({ totalUsers });
  } catch (error) {
    console.error("Error getting total users:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const getUserCountWithChange = async (req, res) => {
  try {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // Date ranges for current and previous month
    const { start: currentStart, end: currentEnd } = getMonthDateRange(currentYear, currentMonth);

    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;
    if (prevMonth < 0) {
      prevMonth = 11;
      prevYear--;
    }
    const { start: prevStart, end: prevEnd } = getMonthDateRange(prevYear, prevMonth);

    // Count students created in current month
    const currentCount = await User.countDocuments({
      createdAt: { $gte: currentStart, $lt: currentEnd }
    });

    // Count students created in previous month
    const prevCount = await User.countDocuments({
      createdAt: { $gte: prevStart, $lt: prevEnd }
    });

    // Calculate percentage change
    let percentageChange = null;
    if (prevCount === 0 && currentCount > 0) {
      percentageChange = 100;
    } else if (prevCount === 0 && currentCount === 0) {
      percentageChange = 0;
    } else {
      percentageChange = ((currentCount - prevCount) / prevCount) * 100;
    }

    res.status(200).json({
      totalUsersThisMonth: currentCount,
      percentageChange: Number(percentageChange.toFixed(2))
    });
  } catch (error) {
    console.error("Error calculating user count change:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get user counts per month for the past 12 months, optionally grouped by role
const getMonthlyUserStarts = async (req, res) => {
  try {
    // Find distinct years present in your User collection for createdAt
    const years = await User.aggregate([
      {
        $group: {
          _id: { $year: "$createdAt" },
        },
      },
      { $sort: { "_id": 1 } }
    ]);

    // Extract years array: [2023, 2024, ...]
    const yearList = years.map(y => y._id);

    // Prepare month names short version
    const monthNamesShort = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    // Create an empty object to store year => monthly data
    const dataByYear = {};

    // For each year, aggregate monthly counts
    for (const year of yearList) {
      const monthlyStats = await User.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(`${year}-01-01`),
              $lt: new Date(`${year + 1}-01-01`),
            },
          },
        },
        {
          $group: {
            _id: { $month: "$createdAt" },
            total: { $sum: 1 },
          },
        },
      ]);

      // Map stats to all 12 months, fill missing months with 0
      const filledStats = monthNamesShort.map((month, idx) => {
        const stat = monthlyStats.find(m => m._id === idx + 1);
        return {
          month,
          users: stat ? stat.total : 0,
        };
      });

      dataByYear[year] = filledStats;
    }

    res.status(200).json(dataByYear);
  } catch (error) {
    console.error("Error getting monthly user starts:", error);
    res.status(500).json({ message: "Failed to get monthly user starts" });
  }
};



module.exports = {
  getAllUsers,
  createUser,
  getParentsByPage,
  getUserById,
  updateUserById,
  deleteUserById,
  deleteMultipleUsers, //the new function
  deleteAllUsers, //the new function
  testUserResponse,
  registerUser,
  getUserByEmail,
  getUserBy_id,
  searchUsers,
  registerParent,
  getTotalUsers,
  getUserCountWithChange,
  getMonthlyUserStarts,
  getParents,
  resetParentPassword,
};
