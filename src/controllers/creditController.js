const Credit = require('../models/Credit');
const mongoose = require('mongoose');
const { getMonthDateRange } = require('../utils/DateRange');
const CreditTransaction = require('../models/CreditTransaction');
// Test response
const testCreditResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is the credit response' });
};

// Get all credits
const getAllCredits = async (req, res) => {
  try {
    const credits = await Credit.find().populate('student_id school_id academicYear_id');
    res.status(200).json(credits);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Create a new credit
const createCredit = async (req, res) => {
  try {
    const { student_id, school_id, academicYear_id, amountPaid } = req.body;

    if (!student_id || !school_id || !academicYear_id || amountPaid === undefined) {
      return res.status(400).json({ message: 'Student ID, School ID, Academic Year ID, and Amount Paid are required.' });
    }

    // Check for duplicate credit
    const existing = await Credit.findOne({ student_id, academicYear_id });
    if (existing) {
      return res.status(409).json({ message: 'Credit already exists for this student in this academic year.' });
    }

    const newCredit = new Credit({
      student_id,
      school_id,
      academicYear_id,
      amountPaid
    });

    await newCredit.save();
    res.status(201).json(newCredit);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get credit by ID
const getCreditById = async (req, res) => {
  try {
    const credit = await Credit.findById(req.params.id).populate('student_id school_id academicYear_id');
    if (!credit) {
      return res.status(404).json({ message: 'Credit record not found' });
    }
    res.status(200).json(credit);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Update credit by ID
const updateCreditById = async (req, res) => {
  try {
    const updatedCredit = await Credit.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedCredit) {
      return res.status(404).json({ message: 'Credit record not found' });
    }
    res.status(200).json(updatedCredit);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete credit by ID
const deleteCreditById = async (req, res) => {
  try {
    const deletedCredit = await Credit.findByIdAndDelete(req.params.id);
    if (!deletedCredit) {
      return res.status(404).json({ message: 'Credit record not found' });
    }
    res.status(200).json({ message: 'Credit record deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete multiple credits
const deleteMultipleCredits = async (req, res) => {
  const { ids } = req.body;
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    const result = await Credit.deleteMany({ _id: { $in: ids } });
    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No credits found for the provided IDs' });
    }
    res.status(200).json({ message: `${result.deletedCount} credit records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get all credits for a specific school
const getCreditsBySchoolId = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const credits = await Credit.find({ school_id }).populate('student_id academicYear_id');
    if (credits.length === 0) {
      return res.status(404).json({ message: 'No credits found for this school' });
    }

    res.status(200).json(credits);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getTotalAmountPaid = async (req, res) => {
  try {
    const totalResult = await CreditTransaction.aggregate([
      {
        $group: {
          _id: null,
          totalAmountPaid: { $sum: "$amountPaid" },
        },
      },
    ]);

    const totalAmountPaid = totalResult.length > 0 ? totalResult[0].totalAmountPaid : 0;
    res.status(200).json({ totalAmountPaid });
  } catch (error) {
    console.error("Error calculating total amount paid:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const getTotalAmountWithChange = async (req, res) => {
  try {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth(); // 0-based month

    // Get date ranges for current and previous months
    const { start: currentStart, end: currentEnd } = getMonthDateRange(currentYear, currentMonth);
    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;
    if (prevMonth < 0) {
      prevMonth = 11;
      prevYear--;
    }
    const { start: prevStart, end: prevEnd } = getMonthDateRange(prevYear, prevMonth);

    // Aggregate total amount for current month
    const currentTotalAgg = await CreditTransaction.aggregate([
      { $match: { createdAt: { $gte: currentStart, $lt: currentEnd } } },
      { $group: { _id: null, total: { $sum: "$amountPaid" } } },
    ]);
    const currentTotal = currentTotalAgg.length > 0 ? currentTotalAgg[0].total : 0;

    // Aggregate total amount for previous month
    const prevTotalAgg = await CreditTransaction.aggregate([
      { $match: { createdAt: { $gte: prevStart, $lt: prevEnd } } },
      { $group: { _id: null, total: { $sum: "$amountPaid" } } },
    ]);
    const prevTotal = prevTotalAgg.length > 0 ? prevTotalAgg[0].total : 0;

    // Calculate percentage change
    let percentageChange = null;
    if (prevTotal === 0 && currentTotal > 0) {
      // If previous total was 0 and current is > 0, consider this as 100% increase or just return null/Infinity
      percentageChange = 100;
    } else if (prevTotal === 0 && currentTotal === 0) {
      percentageChange = 0;
    } else {
      percentageChange = ((currentTotal - prevTotal) / prevTotal) * 100;
    }

    res.status(200).json({
      totalAmount: currentTotal,
      percentageChange: Number(percentageChange.toFixed(2)),
    });
  } catch (error) {
    console.error("Error calculating total amount change:", error);  
    res.status(500).json({ message: "Server error" });
  }
};
module.exports = {
  testCreditResponse,
  getAllCredits,
  createCredit,
  getCreditById,
  updateCreditById,
  deleteCreditById,
  deleteMultipleCredits,
  getCreditsBySchoolId,
  getTotalAmountPaid,
  getTotalAmountWithChange,
};
