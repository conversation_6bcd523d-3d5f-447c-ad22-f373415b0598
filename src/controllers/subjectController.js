// controllers/subjectController.js
const Class = require('../models/Class')
const Subject = require('../models/Subject'); // Subject model
const { ensureUniqueId } = require('../utils/generateId'); 

const testSubjectResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is subject' });
};

// // Get all subjects
const getAllSubjects = async (req, res) => {
  try {
    const subjects = await Subject.find();
    res.json(subjects);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const createSubject = async (req, res) => {
  try {
    const subjectId = await ensureUniqueId(Subject, 'subject_id', 'SBJ');

    // Create and save the subject
    const newSubject = new Subject({
      subject_id: subjectId,
      ...req.body,
    });
    await newSubject.save();

    // Update each class in class_id array to add this subject's _id
    const classIds = req.body.class_id;
    if (Array.isArray(classIds) && classIds.length > 0) {
      await Promise.all(classIds.map(async (classId) => {
        await Class.findByIdAndUpdate(
          classId,
          { $addToSet: { subject_id: newSubject._id } }, // $addToSet avoids duplicates
          { new: true }
        );
      }));
    }

    res.status(201).json(newSubject);
  } catch (err) {
    console.error(err);
    res.status(400).json({ message: err.message });
  }
};


// // Get a subject by ID
const getSubjectById = async (req, res) => {
  try {
    const subject = await Subject.findOne({ subject_id: req.params.id });
    if (!subject) {
      const subjectby_id = await Subject.findById(req.params.id);
      if (!subjectby_id) {
        return res.status(404).json({ message: 'Subject not found' });
      }
      return res.json(subjectby_id);
    } 
    res.json(subject);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
const getSubjectBy_Id = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);
    if (!subject) {
      return res.status(404).json({ message: 'Subject not found' });
    }
    res.status(200).json(subject);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const updateSubjectById = async (req, res) => {
  try {
    // Find the existing subject first to compare classes
    const existingSubject = await Subject.findOne({ subject_id: req.params.id });

    if (!existingSubject) {
      return res.status(404).json({ message: 'Subject not found' });
    }

    // Update the subject with new data
    const updatedSubject = await Subject.findOneAndUpdate(
      { subject_id: req.params.id },
      req.body,
      { new: true }
    );

    // Extract old and new class IDs as strings for comparison
    const oldClassIds = existingSubject.class_id.map(id => id.toString());
    const newClassIds = Array.isArray(req.body.class_id) 
      ? req.body.class_id.map(id => id.toString()) 
      : [];

    // Classes to add the subject to: in newClassIds but not in oldClassIds
    const classesToAdd = newClassIds.filter(id => !oldClassIds.includes(id));

    // Classes to remove the subject from: in oldClassIds but not in newClassIds
    const classesToRemove = oldClassIds.filter(id => !newClassIds.includes(id));

    // Add the subject to the new classes
    await Promise.all(classesToAdd.map(async (classId) => {
      await Class.findByIdAndUpdate(
        classId,
        { $addToSet: { subject_id: updatedSubject._id } }
      );
    }));

    // Remove the subject from classes no longer linked
    await Promise.all(classesToRemove.map(async (classId) => {
      await Class.findByIdAndUpdate(
        classId,
        { $pull: { subject_id: updatedSubject._id } }
      );
    }));

    res.json(updatedSubject);
  } catch (err) {
    console.error(err);
    res.status(400).json({ message: err.message });
  }
};


const deleteSubjectById = async (req, res) => {
  try {
    // Find the subject first to get its _id and classes
    const subjectToDelete = await Subject.findOne({ subject_id: req.params.id });

    if (!subjectToDelete) {
      return res.status(404).json({ message: 'Subject not found' });
    }

    // Remove subject references from all classes linked to this subject
    if (subjectToDelete.class_id && subjectToDelete.class_id.length > 0) {
      await Promise.all(subjectToDelete.class_id.map(async (classId) => {
        await Class.findByIdAndUpdate(
          classId,
          { $pull: { subject_id: subjectToDelete._id } }
        );
      }));
    }

    // Now delete the subject
    await Subject.deleteOne({ subject_id: req.params.id });

    res.json({ message: 'Subject deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};


const deleteMultipleSubjects = async (req, res) => {
  const { ids } = req.body; // array of subject _ids

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Find all subjects to delete to get their _ids and class references
    const subjectsToDelete = await Subject.find({ _id: { $in: ids } });

    if (subjectsToDelete.length === 0) {
      return res.status(404).json({ message: 'No subject records found for the provided IDs' });
    }

    // Remove subject references from classes for each subject
    await Promise.all(subjectsToDelete.map(async (subject) => {
      if (subject.class_id && subject.class_id.length > 0) {
        await Promise.all(subject.class_id.map(async (classId) => {
          await Class.findByIdAndUpdate(
            classId,
            { $pull: { subject_id: subject._id } }
          );
        }));
      }
    }));

    // Delete the subjects
    const result = await Subject.deleteMany({ _id: { $in: ids } });

    res.json({ message: `${result.deletedCount} subject records deleted successfully` });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};


const deleteAllSubjects = async (req, res) => {
  try {
    // Count subjects
    const subjectCount = await Subject.countDocuments();

    if (subjectCount === 0) {
      return res.status(404).json({ message: 'No subjects found to delete' });
    }

    // Get all subject IDs
    const allSubjects = await Subject.find({}, '_id class_id');

    // Collect all class IDs linked to subjects
    const classIdSet = new Set();
    allSubjects.forEach(subject => {
      if (subject.class_id && subject.class_id.length > 0) {
        subject.class_id.forEach(cId => classIdSet.add(cId.toString()));
      }
    });
    const allClassIds = Array.from(classIdSet);

    // Remove all subject references from classes
    if (allClassIds.length > 0) {
      await Promise.all(allClassIds.map(async (classId) => {
        await Class.findByIdAndUpdate(
          classId,
          { $set: { subject_id: [] } } // Clear all subject_ids in those classes
        );
      }));
    }

    // Delete all subjects
    const result = await Subject.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} subject records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};


// Get subjects by school ID
const getSubjectsBySchoolId = async (req, res) => {
  try {
    const { schoolId } = req.params;
    const subjects = await Subject.find({ school_id: schoolId });
    if (subjects.length === 0) {
      return res.status(404).json({ message: 'No subjects found for this school ID' });
    }
    res.json(subjects);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get subjects by class ID
const getSubjectsByClassId = async (req, res) => {
  try {
    const { classId } = req.params;
    const subjects = await Subject.find({ class_id: classId });
    if (subjects.length === 0) {
      return res.status(404).json({ message: 'No subjects found for this class ID' });
    }
    res.json(subjects);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};


module.exports = {
  testSubjectResponse,
  getAllSubjects,
  createSubject,
  getSubjectById,
  updateSubjectById,
  deleteSubjectById,
  deleteMultipleSubjects,
  deleteAllSubjects,
  getSubjectsBySchoolId,
  getSubjectsByClassId,
  getSubjectBy_Id,
};
