"use client";
import { useEffect } from "react";

export default function ThemeInitializer() {
  useEffect(() => {
    // Get saved theme or system preference
    const savedTheme = localStorage.getItem("theme");
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    const theme = savedTheme || systemTheme;
    
    // Apply theme immediately to prevent flash
    if (theme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
    
    // Save the theme if it wasn't already saved
    if (!savedTheme) {
      localStorage.setItem("theme", theme);
    }
  }, []);

  return null; // This component only runs once to apply theme
}
