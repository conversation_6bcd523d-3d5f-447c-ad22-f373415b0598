// Development configuration flags
export const isDevelopment = process.env.NODE_ENV === 'development';

export const developmentConfig = {
    // API endpoints that might not be implemented yet
    experimentalEndpoints: {
        creditSettings: false, // Set to true when backend implements this
        securitySettings: false, // Set to true when backend implements this
        maintenanceSettings: false, // Set to true when backend implements this
    },
    
    // Logging levels
    logLevel: isDevelopment ? 'warn' : 'error',
    
    // Mock data for missing endpoints
    useMockData: true,
};

export const logApiError = (endpoint: string, error: any) => {
    if (isDevelopment) {
        console.warn(`🚧 Development Notice: ${endpoint} endpoint not yet implemented`, error);
    } else {
        console.error(`API Error: ${endpoint}`, error);
    }
};