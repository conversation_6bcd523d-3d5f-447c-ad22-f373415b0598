/**
 * Utility functions for debugging attendance issues in teacher dashboard
 */

import { getAttendanceRecords } from "@/app/services/AttendanceServices";
import { getTeacherPermissions, getTeacherSchedule } from "@/app/services/TeacherPermissionServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import { getPeriodsBySchool } from "@/app/services/PeriodServices";

export interface AttendanceDebugInfo {
  teacherData: any;
  studentsData: any[];
  attendanceData: any[];
  scheduleData: any[];
  periodsData: any[];
  classAnalysis: {
    [classId: string]: {
      className: string;
      studentsCount: number;
      attendanceRecordsCount: number;
      subjectsCount: number;
      periodsCount: number;
      issues: string[];
    };
  };
  overallIssues: string[];
}

/**
 * Debug attendance data for a teacher in a specific school
 */
export async function debugTeacherAttendance(schoolId: string): Promise<AttendanceDebugInfo> {
  console.log("🔍 Starting attendance debug for school:", schoolId);
  
  const debugInfo: AttendanceDebugInfo = {
    teacherData: null,
    studentsData: [],
    attendanceData: [],
    scheduleData: [],
    periodsData: [],
    classAnalysis: {},
    overallIssues: []
  };

  try {
    // Fetch all required data
    console.log("📡 Fetching teacher permissions...");
    debugInfo.teacherData = await getTeacherPermissions(schoolId);
    
    console.log("📡 Fetching students data...");
    debugInfo.studentsData = await getStudentsBySchool(schoolId);
    
    console.log("📡 Fetching attendance records...");
    const attendanceResponse = await getAttendanceRecords(schoolId);
    debugInfo.attendanceData = attendanceResponse.attendance_records || [];
    
    console.log("📡 Fetching teacher schedule...");
    debugInfo.scheduleData = await getTeacherSchedule(schoolId);
    
    console.log("📡 Fetching periods data...");
    debugInfo.periodsData = await getPeriodsBySchool(schoolId);

    // Analyze data
    console.log("🔬 Analyzing data...");
    
    // Check teacher data
    if (!debugInfo.teacherData) {
      debugInfo.overallIssues.push("❌ Teacher data is null or undefined");
    } else {
      if (!debugInfo.teacherData.assigned_classes || debugInfo.teacherData.assigned_classes.length === 0) {
        debugInfo.overallIssues.push("❌ No classes assigned to teacher");
      }
      if (!debugInfo.teacherData.assigned_subjects || debugInfo.teacherData.assigned_subjects.length === 0) {
        debugInfo.overallIssues.push("❌ No subjects assigned to teacher");
      }
    }

    // Analyze each class
    if (debugInfo.teacherData?.assigned_classes) {
      for (const classInfo of debugInfo.teacherData.assigned_classes) {
        const classId = classInfo._id;
        const className = classInfo.name;
        
        // Count students in this class
        const classStudents = debugInfo.studentsData.filter(student => student.class_id === classId);
        
        // Count attendance records for this class
        const classAttendanceByName = debugInfo.attendanceData.filter(record => record.class_name === className);
        const classAttendanceByStudents = debugInfo.attendanceData.filter(record => 
          classStudents.some(student => student._id === record.student_id)
        );
        
        // Count subjects for this class
        const classSubjects = debugInfo.teacherData.assigned_subjects.filter((subject: any) => subject.class_id === classId);
        
        // Count periods for this class
        const classPeriods = debugInfo.scheduleData.filter(schedule => schedule.class_id === classId);
        
        const issues: string[] = [];
        
        if (classStudents.length === 0) {
          issues.push("❌ No students found in this class");
        }
        
        if (classAttendanceByName.length === 0 && classAttendanceByStudents.length === 0) {
          issues.push("❌ No attendance records found for this class");
        }
        
        if (classAttendanceByName.length !== classAttendanceByStudents.length) {
          issues.push(`⚠️ Mismatch in attendance filtering: ${classAttendanceByName.length} by name vs ${classAttendanceByStudents.length} by students`);
        }
        
        if (classSubjects.length === 0) {
          issues.push("❌ No subjects assigned for this class");
        }
        
        if (classPeriods.length === 0) {
          issues.push("❌ No periods/schedule found for this class");
        }
        
        debugInfo.classAnalysis[classId] = {
          className,
          studentsCount: classStudents.length,
          attendanceRecordsCount: Math.max(classAttendanceByName.length, classAttendanceByStudents.length),
          subjectsCount: classSubjects.length,
          periodsCount: classPeriods.length,
          issues
        };
      }
    }

    // Check data consistency
    if (debugInfo.attendanceData.length === 0) {
      debugInfo.overallIssues.push("❌ No attendance records found for the school");
    }
    
    if (debugInfo.studentsData.length === 0) {
      debugInfo.overallIssues.push("❌ No students found for the school");
    }
    
    if (debugInfo.scheduleData.length === 0) {
      debugInfo.overallIssues.push("❌ No schedule data found for the teacher");
    }
    
    if (debugInfo.periodsData.length === 0) {
      debugInfo.overallIssues.push("❌ No periods found for the school");
    }

    // Log summary
    console.log("📊 Debug Summary:");
    console.log("- Teacher assigned classes:", debugInfo.teacherData?.assigned_classes?.length || 0);
    console.log("- Teacher assigned subjects:", debugInfo.teacherData?.assigned_subjects?.length || 0);
    console.log("- Total students:", debugInfo.studentsData.length);
    console.log("- Total attendance records:", debugInfo.attendanceData.length);
    console.log("- Schedule entries:", debugInfo.scheduleData.length);
    console.log("- Periods:", debugInfo.periodsData.length);
    console.log("- Overall issues:", debugInfo.overallIssues.length);
    
    Object.entries(debugInfo.classAnalysis).forEach(([classId, analysis]) => {
      console.log(`📚 Class ${analysis.className}:`, {
        students: analysis.studentsCount,
        attendance: analysis.attendanceRecordsCount,
        subjects: analysis.subjectsCount,
        periods: analysis.periodsCount,
        issues: analysis.issues.length
      });
    });

  } catch (error) {
    console.error("❌ Error during attendance debug:", error);
    debugInfo.overallIssues.push(`❌ Debug error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return debugInfo;
}

/**
 * Debug specific class attendance
 */
export async function debugClassAttendance(schoolId: string, classId: string): Promise<any> {
  console.log("🔍 Debugging class attendance:", { schoolId, classId });
  
  try {
    const [teacherData, studentsData, attendanceResponse, scheduleData, periodsData] = await Promise.all([
      getTeacherPermissions(schoolId),
      getStudentsBySchool(schoolId),
      getAttendanceRecords(schoolId),
      getTeacherSchedule(schoolId),
      getPeriodsBySchool(schoolId)
    ]);

    const attendanceData = attendanceResponse.attendance_records || [];
    const currentClass = teacherData.assigned_classes.find((cls: any) => cls._id === classId);
    
    if (!currentClass) {
      console.error("❌ Class not found in teacher assignments");
      return { error: "Class not found in teacher assignments" };
    }

    const classStudents = studentsData.filter((student: any) => student.class_id === classId);
    const classAttendanceByName = attendanceData.filter((record: any) => record.class_name === currentClass.name);
    const classAttendanceByStudents = attendanceData.filter((record: any) => 
      classStudents.some(student => student._id === record.student_id)
    );

    const result = {
      classInfo: currentClass,
      studentsInClass: classStudents.length,
      attendanceByName: classAttendanceByName.length,
      attendanceByStudents: classAttendanceByStudents.length,
      availableClassNames: [...new Set(attendanceData.map((r: any) => r.class_name))],
      availableStudentIds: [...new Set(attendanceData.map((r: any) => r.student_id))],
      classStudentIds: classStudents.map(s => s._id),
      sampleAttendanceRecord: attendanceData[0] || null,
      sampleStudent: classStudents[0] || null
    };

    console.log("📊 Class Debug Result:", result);
    return result;

  } catch (error) {
    console.error("❌ Error debugging class attendance:", error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Log attendance debug info to console in a formatted way
 */
export function logAttendanceDebug(debugInfo: AttendanceDebugInfo): void {
  console.group("🔍 ATTENDANCE DEBUG REPORT");
  
  console.group("📊 Overall Statistics");
  console.log("Teacher assigned classes:", debugInfo.teacherData?.assigned_classes?.length || 0);
  console.log("Teacher assigned subjects:", debugInfo.teacherData?.assigned_subjects?.length || 0);
  console.log("Total students:", debugInfo.studentsData.length);
  console.log("Total attendance records:", debugInfo.attendanceData.length);
  console.log("Schedule entries:", debugInfo.scheduleData.length);
  console.log("Periods:", debugInfo.periodsData.length);
  console.groupEnd();

  if (debugInfo.overallIssues.length > 0) {
    console.group("❌ Overall Issues");
    debugInfo.overallIssues.forEach(issue => console.log(issue));
    console.groupEnd();
  }

  console.group("📚 Class Analysis");
  Object.entries(debugInfo.classAnalysis).forEach(([classId, analysis]) => {
    console.group(`Class: ${analysis.className} (${classId})`);
    console.log("Students:", analysis.studentsCount);
    console.log("Attendance records:", analysis.attendanceRecordsCount);
    console.log("Subjects:", analysis.subjectsCount);
    console.log("Periods:", analysis.periodsCount);
    if (analysis.issues.length > 0) {
      console.log("Issues:", analysis.issues);
    }
    console.groupEnd();
  });
  console.groupEnd();

  console.groupEnd();
}
