import useAuth from '@/app/hooks/useAuth';
import { usePathname } from 'next/navigation';

export interface ChatbotContext {
  user: {
    id: string;
    role: 'super' | 'admin' | 'teacher' | 'counselor' | 'parent';
    name: string;
    email: string;
    school_ids?: string[];
    permissions: string[];
  };
  dashboard: {
    type: 'super-admin' | 'school-admin' | 'teacher' | 'counselor' | 'parent';
    current_page: string;
    available_actions: string[];
  };
  session: {
    id: string;
    started_at: Date;
    last_activity: Date;
  };
}

export const useChatbotContext = (): ChatbotContext => {
  const { user } = useAuth();
  const pathname = usePathname();
  
  const getDashboardType = (pathname: string): 'super-admin' | 'school-admin' | 'teacher' | 'counselor' | 'parent' => {
    if (pathname.startsWith('/super-admin')) return 'super-admin';
    if (pathname.startsWith('/school-admin')) return 'school-admin';
    if (pathname.startsWith('/teacher')) return 'teacher';
    if (pathname.startsWith('/counselor')) return 'counselor';
    if (pathname.startsWith('/parent')) return 'parent';
    return 'super-admin'; // default
  };

  const getAvailableActions = (dashboardType: string, role: string): string[] => {
    // Logic to determine available actions based on dashboard and role
    const actionMap: Record<string, string[]> = {
      'super-admin': [
        'create_school',
        'manage_users',
        'view_analytics',
        'system_config',
        'manage_subscriptions',
        'bulk_operations',
        'global_reports'
      ],
      'school-admin': [
        'manage_classes',
        'manage_students',
        'manage_teachers',
        'view_reports',
        'manage_fees',
        'school_settings',
        'parent_communication'
      ],
      'teacher': [
        'manage_grades',
        'mark_attendance',
        'plan_lessons',
        'communicate_parents',
        'view_student_progress',
        'manage_assignments',
        'schedule_classes'
      ],
      'counselor': [
        'enroll_students',
        'manage_records',
        'schedule_meetings',
        'provide_guidance',
        'track_academic_progress',
        'disciplinary_actions',
        'family_communication'
      ],
      'parent': [
        'view_child_progress',
        'communicate_teachers',
        'view_assignments',
        'schedule_meetings',
        'view_attendance',
        'pay_fees',
        'update_profile'
      ]
    };
    return actionMap[dashboardType] || [];
  };

  const getUserPermissions = (role: string): string[] => {
    const permissionMap: Record<string, string[]> = {
      'super': [
        'create', 'read', 'update', 'delete',
        'bulk_operations', 'system_admin', 'global_access'
      ],
      'admin': [
        'create', 'read', 'update', 'delete',
        'school_admin', 'manage_school_users'
      ],
      'teacher': [
        'read', 'update', 'manage_classes',
        'grade_students', 'communicate_parents'
      ],
      'counselor': [
        'create', 'read', 'update',
        'enroll_students', 'manage_student_records'
      ],
      'parent': [
        'read', 'view_child_data', 'communicate_school',
        'update_profile', 'pay_fees'
      ]
    };
    return permissionMap[role] || ['read'];
  };

  return {
    user: {
      id: user?._id || '',
      role: user?.role || 'super',
      name: user?.name || '',
      email: user?.email || '',
      school_ids: user?.school_ids || [],
      permissions: getUserPermissions(user?.role || 'super')
    },
    dashboard: {
      type: getDashboardType(pathname),
      current_page: pathname,
      available_actions: getAvailableActions(getDashboardType(pathname), user?.role || 'super')
    },
    session: {
      id: `session_${Date.now()}`,
      started_at: new Date(),
      last_activity: new Date()
    }
  };
};
