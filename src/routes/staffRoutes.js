const express = require('express');
const router = express.Router();
const staffController = require('../controllers/staffController');
const staffPermissionController = require('../controllers/staffPermissionController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

// Test routes
router.get('/test', staffController.testStaffResponse);
router.get('/test-firebase', staffController.testFirebaseIntegration);

// Utility route for syncing existing staff with Firebase
router.post('/sync-firebase/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super']),
  staffController.syncStaffWithFirebase
);

// Utility route for fixing missing staff permissions
router.post('/fix-permissions/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.fixMissingStaffPermissions
);

// GET staff member by ID
router.get('/get-staff/:staff_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super', 'bursar', 'dean_of_studies']),
  staffController.getStaffById
);

// GET teachers available for assignment in a school
router.get('/school/:school_id/teachers',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.getTeachersBySchool
);

// GET all staff for a specific school
router.get('/get-staff-by-school/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super', 'bursar', 'dean_of_studies']),
  staffController.getStaffBySchool
);

// GET search teachers
router.get('/search-teachers',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super', 'dean_of_studies']),
  staffController.searchTeachers
);

// POST create new staff member
router.post('/create-staff',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.createStaff
);

// PUT update staff member
router.put('/update-staff/:staff_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.updateStaff
);

// DELETE staff member (remove from school)
router.delete('/delete-staff/:staff_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.deleteStaff
);

// POST reset staff password
router.post('/reset-password/:staff_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.resetStaffPassword
);

// POST generate access code for teacher
router.post('/generate-access-code',
  authenticate,
  checkSubscription,
  authorize(['admin', 'school_admin', 'super']),
  staffController.generateAccessCode
);

// ===== STAFF PERMISSIONS ROUTES =====

// Test route for staff permissions
router.get('/permissions/test', staffPermissionController.testStaffPermissionResponse);

// GET staff permissions for a specific user in a specific school
router.get('/permissions/user/:userId/school/:schoolId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.getStaffPermissions
);

// GET current user's staff permissions for a specific school
router.get('/permissions/current-user/school/:schoolId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher']),
  staffPermissionController.getCurrentUserStaffPermissions
);

// GET all staff permissions for a school
router.get('/permissions/school/:schoolId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.getSchoolStaffPermissions
);

// POST create staff permissions
router.post('/permissions/create',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.createStaffPermissions
);

// PUT update staff permissions
router.put('/permissions/update/:permissionId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.updateStaffPermissions
);

// DELETE staff permissions (soft delete)
router.delete('/permissions/delete/:permissionId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.deleteStaffPermissions
);

// GET default permissions for a role template
router.get('/permissions/default-permissions/:roleTemplate',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  staffPermissionController.getDefaultPermissionsForRole
);

module.exports = router;
