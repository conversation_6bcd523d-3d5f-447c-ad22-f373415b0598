const express = require('express');
const creditTransactionController = require('../controllers/creditTransactionController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// router.get('/test', creditTransactionController.testCreditTransaction);

// GET all credit transactions
router.get(
  '/get-credit-transactions',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super']),
  creditTransactionController.getAllCreditTransactions
);

// GET credit transaction by ID
router.get(
  '/get-credit-transaction/:id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super']),
  creditTransactionController.getCreditTransactionById
);

// GET credit transactions by School ID
router.get(
  '/get-credit-transactions-by-school/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super']),
  creditTransactionController.getCreditTransactionsBySchoolId
);

// CREATE a credit transaction
router.post(
  '/create-credit-transaction',
  authenticate,
  authorize(['admin', 'super']),
  creditTransactionController.createCreditTransaction
);

// UPDATE a credit transaction
router.put(
  '/update-credit-transaction/:id',
  authenticate,
  authorize(['admin', 'super']),
  creditTransactionController.updateCreditTransactionById
);

// DELETE a credit transaction
router.delete(
  '/delete-credit-transaction/:id',
  authenticate,
  authorize(['admin', 'super']),
  creditTransactionController.deleteCreditTransactionById
); 

// DELETE multiple credit transactions
router.delete(
  '/delete-credit-transactions',
  authenticate,
  authorize(['admin', 'super']),
  creditTransactionController.deleteMultipleCreditTransactions
);

module.exports = router;
