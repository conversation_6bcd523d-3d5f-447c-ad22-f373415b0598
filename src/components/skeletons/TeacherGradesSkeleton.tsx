"use client";

import React from 'react';
import { motion } from 'framer-motion';
import {
  SkeletonCard,
  SkeletonLine,
  SkeletonCircle,
  SkeletonButton,
  SkeletonBadge,
  SkeletonIcon,
  SkeletonHeader,
  SkeletonStatsGrid
} from './SkeletonCard';

interface TeacherGradesSkeletonProps {
  itemCount?: number;
}

const TeacherGradesSkeleton: React.FC<TeacherGradesSkeletonProps> = ({
  itemCount = 8
}) => {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <SkeletonCard>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <SkeletonCircle size="w-12 h-12" />
            <div>
              <SkeletonLine width="w-48" height="h-8" className="mb-2" />
              <SkeletonLine width="w-64" height="h-4" />
            </div>
          </div>
          <SkeletonButton width="w-32" />
        </div>
      </SkeletonCard>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <SkeletonCard>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <SkeletonLine width="w-20" height="h-4" className="mb-2" />
                  <SkeletonLine width="w-16" height="h-8" className="mb-1" />
                  <SkeletonLine width="w-24" height="h-3" />
                </div>
                <SkeletonCircle size="w-10 h-10" />
              </div>
            </SkeletonCard>
          </motion.div>
        ))}
      </div>

      {/* Filters Skeleton */}
      <SkeletonCard>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <SkeletonButton width="w-32" />
            <SkeletonButton width="w-28" />
            <SkeletonButton width="w-36" />
            <SkeletonButton width="w-24" />
          </div>
          <div className="flex space-x-2">
            <SkeletonButton width="w-24" />
            <SkeletonButton width="w-20" />
          </div>
        </div>
      </SkeletonCard>

      {/* Grades Table Skeleton */}
      <SkeletonCard>
        <div className="flex items-center justify-between mb-6">
          <SkeletonLine width="w-32" height="h-6" />
          <div className="flex space-x-2">
            <SkeletonButton width="w-24" />
            <SkeletonButton width="w-28" />
          </div>
        </div>

        {/* Table Header */}
        <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mb-4">
          <SkeletonLine width="w-16" height="h-4" />
          <SkeletonLine width="w-20" height="h-4" />
          <SkeletonLine width="w-16" height="h-4" />
          <SkeletonLine width="w-12" height="h-4" />
          <SkeletonLine width="w-16" height="h-4" />
          <SkeletonLine width="w-20" height="h-4" />
          <SkeletonLine width="w-16" height="h-4" />
        </div>

        {/* Table Rows */}
        <div className="space-y-3">
          {Array.from({ length: itemCount }).map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.05 }}
              className="grid grid-cols-7 gap-4 p-4 border border-stroke rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center space-x-2">
                <SkeletonCircle size="w-8 h-8" />
                <SkeletonLine width="w-20" height="h-4" />
              </div>
              <SkeletonLine width="w-24" height="h-4" />
              <SkeletonBadge />
              <SkeletonLine width="w-8" height="h-6" />
              <SkeletonLine width="w-16" height="h-4" />
              <SkeletonLine width="w-20" height="h-4" />
              <div className="flex space-x-1">
                <SkeletonButton width="w-8" height="h-8" />
                <SkeletonButton width="w-8" height="h-8" />
              </div>
            </motion.div>
          ))}
        </div>
      </SkeletonCard>

      {/* Grade Distribution Chart Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SkeletonCard>
          <SkeletonLine width="w-40" height="h-6" className="mb-6" />
          <div className="h-64">
            <SkeletonLine width="w-full" height="h-full" />
          </div>
        </SkeletonCard>

        <SkeletonCard>
          <SkeletonLine width="w-36" height="h-6" className="mb-6" />
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <SkeletonCircle size="w-4 h-4" />
                  <SkeletonLine width="w-16" height="h-4" />
                </div>
                <SkeletonLine width="w-12" height="h-4" />
              </div>
            ))}
          </div>
        </SkeletonCard>
      </div>

      {/* Pagination Skeleton */}
      <div className="flex items-center justify-between">
        <SkeletonLine width="w-32" height="h-4" />
        <div className="flex space-x-2">
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
        </div>
      </div>
    </div>
  );
};

export default TeacherGradesSkeleton;
