"use client";

import React from "react";

export function TermsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-64"></div>
        </div>
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
      </div>

      {/* Current Term Info */}
      <div className="bg-widget p-4 rounded-lg border border-stroke">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          <div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-1"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
      </div>

      {/* Table */}
      <div className="bg-widget rounded-lg border border-stroke">
        <div className="p-4">
          {/* Table Header */}
          <div className="grid grid-cols-7 gap-4 mb-4 pb-2 border-b border-stroke">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            ))}
          </div>

          {/* Table Rows */}
          {Array.from({ length: 5 }).map((_, rowIndex) => (
            <div key={rowIndex} className="grid grid-cols-7 gap-4 py-3 border-b border-gray-100 dark:border-gray-800">
              {Array.from({ length: 7 }).map((_, colIndex) => (
                <div key={colIndex}>
                  {colIndex === 6 ? (
                    // Status column
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-16"></div>
                  ) : (
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between p-4 border-t border-stroke">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
          <div className="flex items-center gap-2">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
