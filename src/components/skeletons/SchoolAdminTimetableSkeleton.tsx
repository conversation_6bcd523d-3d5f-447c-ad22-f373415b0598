import React from 'react';

// Skeleton for timetable cell
export const TimetableCellSkeleton = () => (
  <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg animate-pulse">
    <div className="space-y-2">
      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
    </div>
  </div>
);

// Skeleton for timetable grid
export const TimetableGridSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between mb-6">
      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="flex space-x-2">
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
      </div>
    </div>

    {/* Days header */}
    <div className="grid grid-cols-6 gap-4 mb-4">
      <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
      {[...Array(5)].map((_, index) => (
        <div key={index} className="h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
      ))}
    </div>

    {/* Time slots */}
    <div className="space-y-4">
      {[...Array(8)].map((_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-6 gap-4">
          <div className="h-16 bg-gray-300 dark:bg-gray-600 rounded flex items-center justify-center">
            <div className="h-4 bg-gray-400 dark:bg-gray-500 rounded w-16"></div>
          </div>
          {[...Array(5)].map((_, colIndex) => (
            <TimetableCellSkeleton key={colIndex} />
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for filters section
export const TimetableFiltersSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-1/4 mb-4"></div>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="space-y-2">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for stats cards
export const TimetableStatsCardSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
    </div>
  </div>
);

// Skeleton for class schedule list
export const ClassScheduleListSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24 mb-1"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
            </div>
          </div>
          <div className="flex space-x-2">
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Main skeleton component for timetable page
export const SchoolAdminTimetableSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
        </div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, index) => (
        <TimetableStatsCardSkeleton key={index} />
      ))}
    </div>

    {/* Filters skeleton */}
    <TimetableFiltersSkeleton />

    {/* Main content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Timetable grid - takes 2 columns */}
      <div className="lg:col-span-2">
        <TimetableGridSkeleton />
      </div>

      {/* Class schedule list - takes 1 column */}
      <div className="lg:col-span-1">
        <ClassScheduleListSkeleton />
      </div>
    </div>
  </div>
);

export default SchoolAdminTimetableSkeleton;
