import React from 'react';

// Skeleton for student attendance card
export const StudentAttendanceCardSkeleton = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-28 mb-1"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
        </div>
      </div>
      <div className="flex space-x-2">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
    </div>
  </div>
);

// Skeleton for attendance calendar
export const AttendanceCalendarSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between mb-6">
      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="flex space-x-2">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    </div>

    {/* Calendar header */}
    <div className="grid grid-cols-7 gap-2 mb-4">
      {[...Array(7)].map((_, index) => (
        <div key={index} className="h-8 bg-gray-300 dark:bg-gray-600 rounded text-center"></div>
      ))}
    </div>

    {/* Calendar days */}
    <div className="grid grid-cols-7 gap-2">
      {[...Array(35)].map((_, index) => (
        <div key={index} className="h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
      ))}
    </div>
  </div>
);

// Skeleton for stats cards
export const AttendanceStatsCardSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
    </div>
  </div>
);

// Skeleton for filters section
export const AttendanceFiltersSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-1/4 mb-4"></div>
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="space-y-2">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for attendance table
export const AttendanceTableSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between mb-6">
      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="flex space-x-2">
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
      </div>
    </div>
    
    {/* Table header */}
    <div className="grid grid-cols-6 gap-4 mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
      ))}
    </div>
    
    {/* Table rows */}
    <div className="space-y-3">
      {[...Array(12)].map((_, index) => (
        <div key={index} className="grid grid-cols-6 gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          {[...Array(6)].map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for attendance chart
export const AttendanceChartSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-6"></div>
    <div className="h-64 bg-gray-300 dark:bg-gray-600 rounded"></div>
  </div>
);

// Skeleton for class attendance overview
export const ClassAttendanceOverviewSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-1"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-8"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for recent attendance activities
export const RecentAttendanceActivitiesSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full mb-1"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
          </div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
        </div>
      ))}
    </div>
  </div>
);

// Main skeleton component for attendance page
export const SchoolAdminAttendanceSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
        </div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, index) => (
        <AttendanceStatsCardSkeleton key={index} />
      ))}
    </div>

    {/* Filters skeleton */}
    <AttendanceFiltersSkeleton />

    {/* Main content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Attendance table - takes 2 columns */}
      <div className="lg:col-span-2">
        <AttendanceTableSkeleton />
      </div>

      {/* Sidebar content - takes 1 column */}
      <div className="lg:col-span-1 space-y-6">
        <ClassAttendanceOverviewSkeleton />
        <RecentAttendanceActivitiesSkeleton />
      </div>
    </div>

    {/* Bottom content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <AttendanceCalendarSkeleton />
      <AttendanceChartSkeleton />
    </div>
  </div>
);

export default SchoolAdminAttendanceSkeleton;
