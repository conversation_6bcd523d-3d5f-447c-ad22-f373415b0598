import React from 'react';

// Skeleton for assignment card
export const AssignmentCardSkeleton = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-32 mb-1"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
        </div>
      </div>
      <div className="flex space-x-2">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
    </div>
  </div>
);

// Skeleton for filters section
export const AssignmentFiltersSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-1/4 mb-4"></div>
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="space-y-2">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for stats cards
export const AssignmentStatsCardSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
    </div>
  </div>
);

// Skeleton for assignment table
export const AssignmentTableSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between mb-6">
      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
    </div>
    
    {/* Table header */}
    <div className="grid grid-cols-7 gap-4 mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
      {[...Array(7)].map((_, index) => (
        <div key={index} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
      ))}
    </div>
    
    {/* Table rows */}
    <div className="space-y-3">
      {[...Array(10)].map((_, index) => (
        <div key={index} className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          {[...Array(7)].map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for teacher list
export const TeacherListSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(8)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-28 mb-1"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
            </div>
          </div>
          <div className="flex space-x-2">
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for class assignment overview
export const ClassAssignmentOverviewSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-8"></div>
          </div>
          <div className="space-y-1">
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Main skeleton component for teacher assignment page
export const SchoolAdminTeacherAssignmentSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
        </div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, index) => (
        <AssignmentStatsCardSkeleton key={index} />
      ))}
    </div>

    {/* Filters skeleton */}
    <AssignmentFiltersSkeleton />

    {/* Main content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Assignment table - takes 2 columns */}
      <div className="lg:col-span-2">
        <AssignmentTableSkeleton />
      </div>

      {/* Sidebar content - takes 1 column */}
      <div className="lg:col-span-1 space-y-6">
        <TeacherListSkeleton />
        <ClassAssignmentOverviewSkeleton />
      </div>
    </div>
  </div>
);

export default SchoolAdminTeacherAssignmentSkeleton;
