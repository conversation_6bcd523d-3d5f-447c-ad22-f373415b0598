"use client";

import React from 'react';

interface SkeletonCardProps {
  className?: string;
  children?: React.ReactNode;
}

interface SkeletonLineProps {
  width?: string;
  height?: string;
  className?: string;
}

interface SkeletonCircleProps {
  size?: string;
  className?: string;
}

interface SkeletonButtonProps {
  width?: string;
  height?: string;
  className?: string;
}

// Base skeleton card component
export const SkeletonCard: React.FC<SkeletonCardProps> = ({ 
  className = "", 
  children 
}) => {
  return (
    <div className={`bg-widget rounded-lg border border-stroke p-4 sm:p-6 ${className}`}>
      {children}
    </div>
  );
};

// Skeleton line component
export const SkeletonLine: React.FC<SkeletonLineProps> = ({ 
  width = "w-full", 
  height = "h-4", 
  className = "" 
}) => {
  return (
    <div 
      className={`${width} ${height} bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${className}`} 
    />
  );
};

// Skeleton circle component
export const SkeletonCircle: React.FC<SkeletonCircleProps> = ({ 
  size = "w-10 h-10", 
  className = "" 
}) => {
  return (
    <div 
      className={`${size} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`} 
    />
  );
};

// Skeleton button component
export const SkeletonButton: React.FC<SkeletonButtonProps> = ({ 
  width = "w-full", 
  height = "h-8", 
  className = "" 
}) => {
  return (
    <div 
      className={`${width} ${height} bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse ${className}`} 
    />
  );
};

// Skeleton badge component
export const SkeletonBadge: React.FC<{ className?: string }> = ({ 
  className = "" 
}) => {
  return (
    <div 
      className={`h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`} 
    />
  );
};

// Skeleton icon component
export const SkeletonIcon: React.FC<{ size?: string; className?: string }> = ({ 
  size = "w-4 h-4", 
  className = "" 
}) => {
  return (
    <div 
      className={`${size} bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${className}`} 
    />
  );
};

// Skeleton header component
export const SkeletonHeader: React.FC<{ className?: string }> = ({ 
  className = "" 
}) => {
  return (
    <SkeletonCard className={className}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <SkeletonCircle size="w-10 h-10 sm:w-12 sm:h-12" />
          <div className="space-y-2">
            <SkeletonLine width="w-32 sm:w-40" height="h-6 sm:h-8" />
            <SkeletonLine width="w-48 sm:w-64" height="h-4" />
          </div>
        </div>
        
        <div className="text-right space-y-1">
          <SkeletonLine width="w-8" height="h-8" />
          <SkeletonLine width="w-20" height="h-4" />
        </div>
      </div>
    </SkeletonCard>
  );
};

// Skeleton stats grid component
export const SkeletonStatsGrid: React.FC<{ 
  itemCount?: number; 
  className?: string 
}> = ({ 
  itemCount = 4, 
  className = "" 
}) => {
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6 ${className}`}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <SkeletonCard key={index}>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <SkeletonLine width="w-16 sm:w-20" height="h-3" />
              <SkeletonLine width="w-8 sm:w-12" height="h-6 sm:h-8" />
            </div>
            <SkeletonCircle size="w-8 h-8 sm:w-10 sm:h-10" />
          </div>
        </SkeletonCard>
      ))}
    </div>
  );
};

// Skeleton list item component
export const SkeletonListItem: React.FC<{ className?: string }> = ({ 
  className = "" 
}) => {
  return (
    <div className={`flex items-center space-x-3 p-3 ${className}`}>
      <SkeletonCircle size="w-8 h-8" />
      <div className="flex-1 space-y-2">
        <SkeletonLine width="w-3/4" height="h-4" />
        <SkeletonLine width="w-1/2" height="h-3" />
      </div>
      <SkeletonIcon />
    </div>
  );
};

// Skeleton table row component
export const SkeletonTableRow: React.FC<{ 
  columns?: number; 
  className?: string 
}> = ({ 
  columns = 4, 
  className = "" 
}) => {
  return (
    <div className={`grid grid-cols-${columns} gap-4 p-4 border-b border-stroke ${className}`}>
      {Array.from({ length: columns }).map((_, index) => (
        <SkeletonLine key={index} height="h-4" />
      ))}
    </div>
  );
};

export default {
  SkeletonCard,
  SkeletonLine,
  SkeletonCircle,
  SkeletonButton,
  SkeletonBadge,
  SkeletonIcon,
  SkeletonHeader,
  SkeletonStatsGrid,
  SkeletonListItem,
  SkeletonTableRow
};
