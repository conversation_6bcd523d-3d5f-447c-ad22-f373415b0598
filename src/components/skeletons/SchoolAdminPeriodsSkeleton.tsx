import React from 'react';

// Skeleton for period card
export const PeriodCardSkeleton = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-1"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
        </div>
      </div>
      <div className="flex space-x-2">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
    </div>
  </div>
);

// Skeleton for period timeline
export const PeriodTimelineSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-6"></div>
    <div className="space-y-4">
      {[...Array(8)].map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-lg flex items-center justify-center">
            <div className="h-4 bg-gray-400 dark:bg-gray-500 rounded w-8"></div>
          </div>
          <div className="flex-1 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
              <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
            </div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for stats cards
export const PeriodStatsCardSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
    </div>
  </div>
);

// Skeleton for period table
export const PeriodTableSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between mb-6">
      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
    </div>
    
    {/* Table header */}
    <div className="grid grid-cols-5 gap-4 mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
      ))}
    </div>
    
    {/* Table rows */}
    <div className="space-y-3">
      {[...Array(8)].map((_, index) => (
        <div key={index} className="grid grid-cols-5 gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          {[...Array(5)].map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for period form
export const PeriodFormSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-6"></div>
    <div className="space-y-4">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="space-y-2">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
          <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
        </div>
      ))}
      <div className="flex space-x-4 pt-4">
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
        <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
      </div>
    </div>
  </div>
);

// Skeleton for period conflicts
export const PeriodConflictsSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-6 h-6 bg-red-300 dark:bg-red-600 rounded"></div>
            <div className="h-4 bg-red-300 dark:bg-red-600 rounded w-32"></div>
          </div>
          <div className="h-3 bg-red-300 dark:bg-red-600 rounded w-full"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for period usage chart
export const PeriodUsageChartSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-6"></div>
    <div className="space-y-4">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
          <div className="flex-1 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-8"></div>
        </div>
      ))}
    </div>
  </div>
);

// Main skeleton component for periods page
export const SchoolAdminPeriodsSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
        </div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, index) => (
        <PeriodStatsCardSkeleton key={index} />
      ))}
    </div>

    {/* Main content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Period timeline - takes 2 columns */}
      <div className="lg:col-span-2">
        <PeriodTimelineSkeleton />
      </div>

      {/* Sidebar content - takes 1 column */}
      <div className="lg:col-span-1 space-y-6">
        <PeriodFormSkeleton />
        <PeriodConflictsSkeleton />
      </div>
    </div>

    {/* Bottom content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <PeriodTableSkeleton />
      <PeriodUsageChartSkeleton />
    </div>
  </div>
);

export default SchoolAdminPeriodsSkeleton;
