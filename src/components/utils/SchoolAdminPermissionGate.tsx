"use client";

import React from "react";
import useSchoolAdminPermissions, { SchoolAdminPermissions } from "@/app/hooks/useSchoolAdminPermissions";
import { Lock } from "lucide-react";

interface SchoolAdminPermissionGateProps {
  children: React.ReactNode;
  module?: keyof SchoolAdminPermissions;
  action?: string;
  requiredPermissions?: { module: keyof SchoolAdminPermissions; action: string }[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean;
  roles?: string[]; // Alternative: check by roles instead of permissions
}

export default function SchoolAdminPermissionGate({
  children,
  module,
  action,
  requiredPermissions,
  requireAll = true,
  fallback,
  showFallback = false,
  roles,
}: SchoolAdminPermissionGateProps) {
  const { hasPermission, canAccess, userRole, isLoading } = useSchoolAdminPermissions();

  // Show loading state
  if (isLoading) {
    return showFallback ? (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal"></div>
      </div>
    ) : null;
  }

  // Check by roles first (if provided)
  if (roles && roles.length > 0) {
    const hasRole = userRole && roles.includes(userRole);
    if (!hasRole) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Access Restricted</h3>
              <p className="text-foreground/60">
                This feature is only available to {roles.join(', ')} users.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // Check by specific module and action
  if (module && action) {
    const hasAccess = hasPermission(module, action);
    if (!hasAccess) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Permission Required</h3>
              <p className="text-foreground/60">
                You don't have permission to {action.replace(/_/g, ' ')} in {module.replace(/_/g, ' ')}.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // Check by multiple permissions
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasAccess = requireAll 
      ? canAccess(requiredPermissions)
      : requiredPermissions.some(({ module, action }) => hasPermission(module, action));

    if (!hasAccess) {
      return showFallback ? (
        fallback || (
          <div className="flex items-center justify-center p-8 text-center">
            <div className="max-w-sm">
              <Lock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Insufficient Permissions</h3>
              <p className="text-foreground/60">
                You don't have the required permissions to access this feature.
              </p>
            </div>
          </div>
        )
      ) : null;
    }
    return <>{children}</>;
  }

  // If no specific permissions are required, show children
  return <>{children}</>;
}

// Convenience components for common permission checks
export function StudentManagementGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="students" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function StaffManagementGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="staff" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function AcademicRecordsGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="academic_records" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function FinancialsGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="financials" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function CommunicationsGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="communications" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function SchoolManagementGate({ children, action, showFallback = false }: {
  children: React.ReactNode;
  action: string;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate module="school_management" action={action} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function SchoolAdminOnlyGate({ children, showFallback = false }: {
  children: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate roles={['admin', 'super', 'school_admin']} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function BursarOnlyGate({ children, showFallback = false }: {
  children: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate roles={['admin', 'super', 'bursar']} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}

export function DeanOfStudiesOnlyGate({ children, showFallback = false }: {
  children: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <SchoolAdminPermissionGate roles={['admin', 'super', 'dean_of_studies']} showFallback={showFallback}>
      {children}
    </SchoolAdminPermissionGate>
  );
}
