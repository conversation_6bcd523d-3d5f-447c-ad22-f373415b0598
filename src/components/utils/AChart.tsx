"use client";

import React, { useState, useEffect } from "react";
import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";
import { getMonthlyUserStarts, MonthlyUserStatsByYear, MonthlyUserStat } from "@/app/services/UserServices"; // update path accordingly

const monthShortNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

const AChart = () => {
  const [dataByYear, setDataByYear] = useState<MonthlyUserStatsByYear>({});
  const [selectedYear, setSelectedYear] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Transform backend month full names to short names expected by chart
  const transformData = (rawData: MonthlyUserStatsByYear): MonthlyUserStatsByYear => {
    const transformed: MonthlyUserStatsByYear = {};
    for (const year in rawData) {
      transformed[year] = rawData[year].map(({ month, users }) => {
        // convert full month name to short month name (e.g., "January" → "Jan")
        const shortMonth = monthShortNames.find(
          (short, i) => month.toLowerCase().startsWith(short.toLowerCase())
        ) ?? month.slice(0, 3);

        return {
          month: shortMonth,
          users: users,
        };
      });
    }
    return transformed;
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const rawData = await getMonthlyUserStarts();
        const transformedData = transformData(rawData);
        setDataByYear(transformedData);

        // Default to the latest year available
        const years = Object.keys(transformedData).sort((a, b) => +b - +a);
        setSelectedYear(years[0] ?? "");
      } catch (err) {
        setError("Failed to load data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) return <div>Loading user stats...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="flex flex-col gap-3">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">User Growth Trend</h2>
        <select
          value={selectedYear}
          onChange={(e) => setSelectedYear(e.target.value)}
          className="
            text-sm
            border
            rounded-md
            pl-3
            pr-8
            py-1
            appearance-none
            cursor-pointer
            w-24
            text-sm
            focus:outline-none focus:ring-1 focus:ring-foreground focus:border-foreground
            bg-white
            text-gray-900
            dark:bg-gray-800
            dark:text-white
          "
        >
          {Object.keys(dataByYear).map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </div>

      <div className="w-full h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={dataByYear[selectedYear] ?? []}
            margin={{
              top: 0,
              right: 0,
              left: -25,
              bottom: 0,
            }}
          >
            <defs>
              <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#01B574" stopOpacity={0.5} />
                <stop offset="80%" stopColor="#01B574" stopOpacity={0} />
              </linearGradient>
            </defs>
            <XAxis dataKey="month" axisLine={false} tick={{ fontSize: 10 }} />
            <YAxis tick={{ fontSize: 10 }} />
            <Tooltip
              contentStyle={{
                backgroundColor: "bg-background",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "12px",
              }}
              labelStyle={{
                color: "text-foreground",
              }}
            />
            <Area
              type="monotone"
              dataKey="users"
              stroke="#01B574"
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#colorUsers)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default AChart;
