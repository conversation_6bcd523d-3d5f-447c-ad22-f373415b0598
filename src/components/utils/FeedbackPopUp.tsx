import { X } from "lucide-react";
import { pdf, PDFDownloadLink } from "@react-pdf/renderer";
import ReceiptPDF from "./RecieptPDF";
import SubmissionFeedback from "../widgets/SubmissionFeedback";
// Type definitions (adjust or import as needed)
interface Student {
  student_id: string;
  first_name: string;
  last_name: string;
  class_level?: string;
  class_id?: string;
}

interface School {
  name: string;
  logoUrl?: string;
}

interface ReceiptData {
  paidInstallmentNumber: number | undefined;
  installmentDates: string[] | undefined;
  installments: number | undefined;
  scholarshipPercentage: number | undefined;
  applyScholarship: boolean | undefined;
  student: Student;
  school: School;
  paymentItems: { description: string; amount: number }[];
  receiptId: string;
  date: string | number | Date;
  taxRate?: number;
}

interface FeedbackPopupProps {
  status: "success" | "error";
  message: string;
  onClose: () => void;
  receiptData?: ReceiptData;
}

const FeedbackPopup: React.FC<FeedbackPopupProps> = ({
  status,
  message,
  onClose,
  receiptData,
}) => {
  const showPopup = status === "success" || status === "error";

  if (!showPopup) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-background text-foreground p-6 rounded shadow-md max-w-sm w-full">
        {/* Header */}
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold text-foreground">
            {status === "success" ? "Success" : "Error"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* Lottie Animation + Message */}
        <SubmissionFeedback
          status={status === "success" ? "success" : "failure"}
          message={message}
        />

        {/* Buttons */}
        {status === "success" && receiptData && (
          <div className="mt-4 space-y-2">
            <PDFDownloadLink
              document={
                <ReceiptPDF
                    student={receiptData.student}
                    school={receiptData.school}
                    paymentItems={receiptData.paymentItems}
                    receiptId={receiptData.receiptId}
                    date={receiptData.date}
                    taxRate={receiptData.taxRate || 0}
                    applyScholarship={receiptData.applyScholarship}
                    scholarshipPercentage={receiptData.scholarshipPercentage}
                    installments={receiptData.installments}
                    installmentDates={receiptData.installmentDates}
                    paidInstallmentNumber={receiptData.paidInstallmentNumber}
                />
              }
              fileName={`receipt-${receiptData.receiptId}.pdf`}
              className="block text-center text-white bg-green-600 hover:bg-green-700 px-4 py-2 rounded-md text-sm w-full"
            >
              {({ loading }) => loading ? "Preparing PDF..." : "Download Receipt"}
            </PDFDownloadLink>

            <button
              onClick={async () => {
                const receiptDoc = (
                  <ReceiptPDF
                    student={receiptData.student}
                    school={receiptData.school}
                    paymentItems={receiptData.paymentItems}
                    receiptId={receiptData.receiptId}
                    date={receiptData.date}
                    taxRate={receiptData.taxRate || 0}
                    applyScholarship={receiptData.applyScholarship}
                    scholarshipPercentage={receiptData.scholarshipPercentage}
                    installments={receiptData.installments}
                    installmentDates={receiptData.installmentDates}
                    paidInstallmentNumber={receiptData.paidInstallmentNumber}
                  />
                );
                const blob = await pdf(receiptDoc).toBlob();
                const url = URL.createObjectURL(blob);
                const newTab = window.open("", "_blank");
                if (newTab) {
                  newTab.location.href = url;
                } else {
                  alert("Popup blocked! Please allow popups to view the receipt.");
                }
              }}
              className="block text-center text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-sm w-full"
            >
              View Receipt
            </button>
          </div>
        )}

        {/* OK Button */}
        <div className="mt-4 text-right">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-foreground text-background rounded-md hover:bg-teal-600"
          >
            OK
          </button>
        </div>
      </div>
    </div>

  );
};

export default FeedbackPopup;
