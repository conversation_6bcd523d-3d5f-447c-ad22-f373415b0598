// utils/getEntityNames.ts

export type Entity = {
  _id: any; // string or ObjectId
  name: string;
};

export function getEntityNames(
  ids: (string | any)[],
  entities: Entity[],
  entityLabel: string
): string {
  if (!ids || ids.length === 0) return `No ${entityLabel}s`;

  const entityMap = new Map<string, string>();
  entities.forEach(ent => {
    if (ent._id && ent.name) {
      entityMap.set(ent._id.toString(), ent.name);
    }
  });

  const names = ids.map(id => {
    const key = id.toString();
    return entityMap.get(key) ?? `Unknown ${entityLabel} (${key})`;
  });

  return names.join(", ");
}
