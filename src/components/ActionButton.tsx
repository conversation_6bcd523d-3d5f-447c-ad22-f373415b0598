'use client';

import { motion } from 'framer-motion';
import {
  Pencil,
  Trash2,
  X,
  Save,
  Plus,
  ArrowUpRight,
  Eye,
  KeyRound, // ✅ Added Key icon for reset password
} from 'lucide-react';
import clsx from 'clsx';
import CircularLoader from '@/components/widgets/CircularLoader';

type ActionType =
  | 'edit'
  | 'delete'
  | 'cancel'
  | 'save'
  | 'add'
  | 'sendCredit'
  | 'view'
  | 'resetPassword'; // ✅ Added resetPassword

interface ActionButtonProps {
  action: ActionType;
  label?: string;
  onClick?: () => void;
  type?: 'button' | 'submit';
  isLoading?: boolean;
  disabled?: boolean;
}

const actionStyles: Record<
  ActionType,
  {
    label: string;
    icon: JSX.Element;
    classes: string;
  }
> = {
  edit: {
    label: 'Edit',
    icon: <Pencil size={16} />,
    classes: 'bg-teal hover:bg-teal/90 text-white',
  },
  delete: {
    label: 'Delete',
    icon: <Trash2 size={16} />,
    classes: 'bg-red-500 hover:bg-red-600 text-white',
  },
  cancel: {
    label: 'Cancel',
    icon: <X size={16} />,
    classes:
      'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500',
  },
  save: {
    label: 'Save',
    icon: <Save size={16} />,
    classes: 'bg-teal text-white hover:bg-teal-600',
  },
  add: {
    label: 'Add',
    icon: <Plus size={16} />,
    classes: 'bg-teal text-white hover:bg-teal-600',
  },
  sendCredit: {
    label: 'Send Credit',
    icon: <ArrowUpRight size={16} />,
    classes: 'bg-teal text-white hover:bg-teal-600',
  },
  view: {
    label: 'View',
    icon: <Eye size={16} />,
    classes: 'bg-blue-500 hover:bg-blue-600 text-white',
  },
  resetPassword: {
    label: 'Reset Password',
    icon: <KeyRound size={16} />,
    classes: 'bg-yellow-500 hover:bg-yellow-600 text-white',
  },
};

export default function ActionButton({
  action,
  label,
  onClick,
  type = 'button',
  isLoading = false,
  disabled = false,
}: ActionButtonProps) {
  const config = actionStyles[action];

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: 'spring', stiffness: 300 }}
      type={type}
      onClick={onClick}
      className={clsx(
        'inline-flex items-center gap-2 px-4 py-2 rounded-md transition-colors',
        config.classes,
        (disabled || isLoading) && 'opacity-50 cursor-not-allowed'
      )}
      disabled={disabled || isLoading}
    >
      {isLoading && action === 'save' ? (
        <>
          <CircularLoader size={18} color="teal-500" />
          Saving...
        </>
      ) : (
        <>
          <span className="w-4 h-4">{config.icon}</span>
          {label ?? `${config.label}`}
        </>
      )}
    </motion.button>
  );
}
