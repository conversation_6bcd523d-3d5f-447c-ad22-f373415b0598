'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

export default function BackButton({ label = 'Go Back' }: { label?: string }) {
  const router = useRouter();

  return (
    <button
      onClick={() => router.back()}
      className="inline-flex items-center gap-2 px-4 py-2 border border-gray-500 text-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
    >
      <ArrowLeft className="w-4 h-4" />
      {label}
    </button>
  );
}
