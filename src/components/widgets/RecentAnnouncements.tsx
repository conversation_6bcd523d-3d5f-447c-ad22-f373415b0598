"use client";

import React, { useState, useEffect } from "react";
import { Megaphone, Calendar, AlertCircle, Info, AlertTriangle, Zap } from "lucide-react";
import { getRecentAnnouncementsBySchool, AnnouncementSchema } from "@/app/services/AnnouncementServices";
import CircularLoader from "@/components/widgets/CircularLoader";

interface RecentAnnouncementsProps {
  schoolId: string;
}

const RecentAnnouncements: React.FC<RecentAnnouncementsProps> = ({ schoolId }) => {
  const [announcements, setAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecentAnnouncements = async () => {
      if (!schoolId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const recentAnnouncements = await getRecentAnnouncementsBySchool(schoolId);
        setAnnouncements(recentAnnouncements);
      } catch (err) {
        console.error("Error fetching recent announcements:", err);
        setError("Failed to load recent announcements");
      } finally {
        setLoading(false);
      }
    };

    fetchRecentAnnouncements();
  }, [schoolId]);

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Zap className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'high':
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">{error}</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-stroke bg-widget p-4">
      <div className="mb-4 flex items-center gap-2">
        <Megaphone className="h-5 w-5 text-[#17B890]" />
        <h3 className="text-lg font-semibold text-foreground">Recent Announcements</h3>
      </div>
      
      {announcements.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Megaphone className="h-12 w-12 text-foreground/30 mb-2" />
          <p className="text-foreground/60 text-center">No recent announcements</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-[320px] overflow-y-auto custom-scrollbar">
          {announcements.map((announcement) => (
            <div
              key={announcement._id}
              className={`border-l-4 p-3 rounded-r-lg transition-all duration-200 hover:shadow-sm ${getPriorityColor(announcement.priority)}`}
            >
              <div className="flex items-start justify-between gap-2 mb-2">
                <div className="flex items-center gap-2">
                  {getPriorityIcon(announcement.priority)}
                  <h4 className="font-medium text-foreground text-sm line-clamp-1">
                    {announcement.title}
                  </h4>
                </div>
                <span className="text-xs text-foreground/60 whitespace-nowrap">
                  {formatDate(announcement.published_at || announcement.created_at)}
                </span>
              </div>
              
              <p className="text-sm text-foreground/70 mb-2 line-clamp-2">
                {truncateContent(announcement.content)}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                    announcement.priority === 'urgent' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                    announcement.priority === 'high' ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300' :
                    announcement.priority === 'medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                  }`}>
                    {announcement.priority.charAt(0).toUpperCase() + announcement.priority.slice(1)}
                  </span>
                  <span className="text-xs text-foreground/50">
                    {announcement.target_audience}
                  </span>
                </div>
                
                <div className="flex items-center gap-1 text-foreground/50">
                  <Calendar className="h-3 w-3" />
                  <span className="text-xs">
                    {formatDate(announcement.published_at || announcement.created_at)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentAnnouncements;
