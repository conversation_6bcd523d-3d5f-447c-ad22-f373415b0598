"use client";

import React, { useState, useEffect } from "react";
import { X, FileCheck2, Save, Users, Search, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  attendance?: any | null;
  students: any[];
  classes: any[];
  subjects: any[];
  schedules: any[];
  periods: any[];
  loading?: boolean;
  isTeacherMode?: boolean; // New prop to indicate teacher mode
  preSelectedClass?: string; // Pre-selected class for teachers
}

// Days of the week
const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

export default function AttendanceModal({
  isOpen,
  onClose,
  onSubmit,
  attendance,
  students,
  classes,
  subjects,
  schedules,
  periods,
  loading = false
}: AttendanceModalProps) {
  
  // Form state
  const [formData, setFormData] = useState({
    day_of_week: "",
    class_id: "",
    period_id: "",
    subject_id: "",
    date: new Date().toISOString().split('T')[0],
    academic_year: new Date().getFullYear() + "-" + (new Date().getFullYear() + 1)
  });
  console.log('Attendance data:', attendance);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Student attendance states - each student has one status
  const [studentAttendance, setStudentAttendance] = useState<Record<string, string>>({});
  const [bulkStatus, setBulkStatus] = useState<"Present" | "Absent" | "Late" | "Excused">("Present");

  // Search states
  const [classSearch, setClassSearch] = useState('');
  const [showClassDropdown, setShowClassDropdown] = useState(false);

  const isEditing = !!attendance;

  // Initialize form data
  useEffect(() => {
    if (isOpen) {
      if (isEditing && attendance) {
        // For editing, we need to extract data from the attendance record
        let scheduleData = null;
        let dayOfWeek = "";
        let classId = "";
        let periodId = "";
        let subjectId = "";

        // If attendance has schedule_id, find the corresponding schedule
        if (attendance.schedule_id) {
          scheduleData = schedules.find(s => s._id === attendance.schedule_id);

          if (scheduleData) {
            dayOfWeek = scheduleData.day_of_week || "";
            classId = scheduleData.class_id || "";
            periodId = scheduleData._id; // Use schedule ID as period_id for the dropdown
            subjectId = scheduleData.subject_id || "";
          }
        }

        // If no schedule found, try to extract from attendance directly (fallback)
        if (!scheduleData) {
          dayOfWeek = attendance.day_of_week || "";
          classId = attendance.class_id || "";
          periodId = attendance.period_id || "";
          subjectId = attendance.subject_id || "";

          // If still no data, try to find by names
          if (!classId && attendance.class_name) {
            const foundClass = classes.find(c => c.name === attendance.class_name);
            classId = foundClass?._id || "";
          }

          if (!subjectId && attendance.subject_name) {
            const foundSubject = subjects.find(s => s.name === attendance.subject_name);
            subjectId = foundSubject?._id || "";
          }

          // Try to find a schedule that matches the class, subject, and period
          if (classId && subjectId && attendance.period_number) {
            const matchingSchedule = schedules.find(s => {
              const period = periods.find(p => p._id === s.period_id);
              return s.class_id === classId &&
                     s.subject_id === subjectId &&
                     period?.period_number === attendance.period_number;
            });

            if (matchingSchedule) {
              periodId = matchingSchedule._id;
              dayOfWeek = matchingSchedule.day_of_week || "";
            }
          }
        }

        // Format date properly
        let formattedDate = new Date().toISOString().split('T')[0];
        if (attendance.date) {
          const dateObj = new Date(attendance.date);
          if (!isNaN(dateObj.getTime())) {
            formattedDate = dateObj.toISOString().split('T')[0];
          }
        }



        setFormData({
          day_of_week: dayOfWeek,
          class_id: classId,
          period_id: periodId,
          subject_id: subjectId,
          date: formattedDate,
          academic_year: attendance.academic_year || "2024-2025"
        });

        // Initialize student attendance from existing data
        const attendanceMap: Record<string, string> = {};

        if (attendance.students && Array.isArray(attendance.students)) {
          // Multiple students (bulk attendance)
          attendance.students.forEach((student: any) => {
            attendanceMap[student.student_id] = student.status;
          });
        } else if (attendance.student_id && attendance.status) {
          // Single student attendance
          attendanceMap[attendance.student_id] = attendance.status;
        }


        setStudentAttendance(attendanceMap);

      } else {
        // Reset form for new attendance
        setFormData({
          day_of_week: "",
          class_id: "",
          period_id: "",
          subject_id: "",
          date: new Date().toISOString().split('T')[0],
          academic_year: "2024-2025"
        });
        setStudentAttendance({});
      }
      setErrors({});
      setClassSearch('');
    }
  }, [isOpen, isEditing, attendance, schedules]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      
      // Don't close if clicking inside any dropdown or search input
      if (target.closest('.dropdown-container') || target.closest('.search-input')) {
        return;
      }
      
      setShowClassDropdown(false);
    };

    if (showClassDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showClassDropdown]);

  // Get filtered students for selected class
  const getClassStudents = () => {
    if (!formData.class_id) return [];
    return students.filter(student => student.class_id === formData.class_id);
  };

  // Get periods for selected class and day
  const getClassPeriods = () => {
    if (!formData.class_id || !formData.day_of_week) return [];

    const filteredSchedules = schedules.filter(schedule =>
      schedule.class_id === formData.class_id &&
      schedule.day_of_week === formData.day_of_week
    );



    return filteredSchedules;
  };

  // Get subjects for selected period, class and day
  const getPeriodSubjects = () => {
    if (!formData.period_id || !formData.class_id || !formData.day_of_week) return [];

    // Get all schedules for this class, day and period
    const periodSchedules = schedules.filter(s =>
      s.class_id === formData.class_id &&
      s.day_of_week === formData.day_of_week &&
      s.period_id === formData.period_id
    );

    // If no schedules found, try a more flexible approach
    if (periodSchedules.length === 0) {
      // Try to find schedules for this class and day, regardless of period
      const daySchedules = schedules.filter(s =>
        s.class_id === formData.class_id &&
        s.day_of_week === formData.day_of_week
      );

      // If still no schedules, return all subjects for this class
      if (daySchedules.length === 0) {
        return subjects;
      }

      // Get subjects from day schedules
      const subjectIds = [...new Set(daySchedules.map(s => s.subject_id))];
      return subjects.filter(subject => subjectIds.includes(subject._id));
    }

    // Get unique subjects from these schedules
    const subjectIds = [...new Set(periodSchedules.map(s => s.subject_id))];
    const filteredSubjects = subjects.filter(subject => subjectIds.includes(subject._id));

    return filteredSubjects;
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Only clear related fields when parent changes and we're not editing
    if (!isEditing) {
      if (field === 'day_of_week') {
        setFormData(prev => ({ ...prev, period_id: "", subject_id: "" }));
        setStudentAttendance({});
      } else if (field === 'class_id') {
        setFormData(prev => ({ ...prev, period_id: "", subject_id: "" }));
        setStudentAttendance({});
      } else if (field === 'period_id') {
        setFormData(prev => ({ ...prev, subject_id: "" }));
      }
    }

    // Clear errors
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Handle student attendance change
  const handleStudentAttendance = (studentId: string, status: string) => {
    setStudentAttendance(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  // Apply bulk status to all students
  const applyBulkStatus = () => {
    const currentStudents = getClassStudents();
    const newAttendance: Record<string, string> = {};

    currentStudents.forEach(student => {
      newAttendance[student._id] = bulkStatus;
    });

    setStudentAttendance(newAttendance);
  };

  // Validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.day_of_week) {
      newErrors.day_of_week = "Please select a day";
    }

    if (!formData.class_id) {
      newErrors.class_id = "Please select a class";
    }

    if (!formData.period_id) {
      newErrors.period_id = "Please select a period";
    }

    if (!formData.subject_id) {
      newErrors.subject_id = "Please select a subject";
    }

    if (!formData.date) {
      newErrors.date = "Please select a date";
    }

    if (!formData.academic_year) {
      newErrors.academic_year = "Please enter academic year";
    }

    // Check if at least one student has attendance marked
    const hasAttendance = Object.keys(studentAttendance).length > 0;
    if (!hasAttendance) {
      newErrors.students = "Please mark attendance for at least one student";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      // Format data for API
      let actualPeriodId = formData.period_id;

      // If period_id is actually a schedule_id, extract the real period_id
      const selectedSchedule = schedules.find(s => s._id === formData.period_id);
      if (selectedSchedule) {
        actualPeriodId = selectedSchedule.period_id;
      }

      if (isEditing && attendance) {
        // For editing, we update the attendance record
        const studentEntries = Object.entries(studentAttendance);

        if (studentEntries.length === 1) {
          // Single student update - pass the data in the format expected by updateAttendance
          const [studentId, status] = studentEntries[0];
          console.log("school id = ", attendance.school_id)
          const updateData = {
            _id: attendance._id,
            school_id: attendance.school_id,
            student_id: studentId,
            status: status,
            date: formData.date,
            academic_year: formData.academic_year
          };

          // The page will handle calling updateAttendance with attendance._id and updateData
          await onSubmit(updateData);
        } else {
          // Multiple students - this would need a different approach
          // For now, we'll treat it as a new attendance creation
          const attendanceData = {
            day_of_week: formData.day_of_week,
            class_id: formData.class_id,
            period_id: actualPeriodId,
            subject_id: formData.subject_id,
            date: formData.date,
            academic_year: formData.academic_year,
            school_id: attendance.school_id,
            students: Object.entries(studentAttendance).map(([studentId, status]) => ({
              student_id: studentId,
              status: status
            }))
          };

          await onSubmit(attendanceData);
        }
      } else {
        // Creating new attendance
        const attendanceData = {
          day_of_week: formData.day_of_week,
          class_id: formData.class_id,
          period_id: actualPeriodId,
          subject_id: formData.subject_id,
          date: formData.date,
          academic_year: formData.academic_year,
          students: Object.entries(studentAttendance).map(([studentId, status]) => ({
            student_id: studentId,
            status: status
          }))
        };

        await onSubmit(attendanceData);
      }

      onClose();
    } catch (error) {
      console.error("Error submitting attendance:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper functions
  const getSelectedClassName = () => {
    const selectedClass = classes.find(cls => cls._id === formData.class_id);

    return selectedClass?.name || "";
  };

  const getSelectedPeriodName = () => {
    const selectedSchedule = schedules.find(s => s._id === formData.period_id);
    if (!selectedSchedule) return "";

    // Handle both populated and non-populated period_id
    let period;
    if (typeof selectedSchedule.period_id === 'object' && selectedSchedule.period_id._id) {
      // period_id is populated
      period = selectedSchedule.period_id;
    } else {
      // period_id is just an ID string, find in periods array
      period = periods.find(p => p._id === selectedSchedule.period_id);
    }

    return period ? `Period ${period.period_number} (${period.start_time?.slice(0,5) || period.start_time}-${period.end_time?.slice(0,5) || period.end_time})` : "";
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(s => s._id === formData.subject_id);

    return selectedSubject?.name || "";
  };

  // Filter classes for search
  const filteredClasses = classes.filter(cls =>
    cls.name.toLowerCase().includes(classSearch.toLowerCase())
  );

  const classStudents = getClassStudents();
  const classPeriods = getClassPeriods();
  const periodSubjects = getPeriodSubjects();

  // For editing individual attendance, show only the specific student
  const studentsToShow = isEditing && attendance && attendance.student_id
    ? classStudents.filter(student => student._id === attendance.student_id)
    : classStudents;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={(e) => {
              const target = e.target as HTMLElement;
              // Don't close if clicking inside dropdown or search input
              if (target.closest('.dropdown-container') || target.closest('.search-input')) {
                return;
              }
              onClose();
            }}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <FileCheck2 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Attendance" : "Mark Attendance"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update attendance records" : "Mark attendance for class period"}
                  </p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Day and Class Selection */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Day Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Day <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.day_of_week}
                    onChange={(e) => handleInputChange("day_of_week", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.day_of_week
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    } ${isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={isEditing}
                  >
                    <option value="">Select day</option>
                    {DAYS_OF_WEEK.map((day) => (
                      <option key={day} value={day}>
                        {day}
                      </option>
                    ))}
                  </select>
                  {errors.day_of_week && (
                    <p className="mt-1 text-sm text-red-500">{errors.day_of_week}</p>
                  )}
                </div>
                {/* Class Selection */}
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Class <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div
                      className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                        errors.class_id
                          ? "border-red-500 dark:border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } ${isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                      onClick={(e) => {
                        if (isEditing) return;
                        e.stopPropagation();
                        setShowClassDropdown(!showClassDropdown);
                      }}
                    >
                      <span className="text-gray-900 dark:text-white">
                        {getSelectedClassName() || "Select class"}
                      </span>
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    </div>

                    {showClassDropdown && !isEditing && (
                      <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                        <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <input
                              type="text"
                              placeholder="Search classes..."
                              value={classSearch}
                              onChange={(e) => setClassSearch(e.target.value)}
                              className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white text-sm"
                              onClick={(e) => e.stopPropagation()}
                              onFocus={(e) => e.stopPropagation()}
                            />
                          </div>
                        </div>
                        <div className="max-h-48 overflow-y-auto">
                          {filteredClasses.length > 0 ? (
                            filteredClasses.map((cls) => (
                              <div
                                key={cls._id}
                                className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleInputChange("class_id", cls._id);
                                  setShowClassDropdown(false);
                                  setClassSearch('');
                                }}
                              >
                                <div className="font-medium">{cls.name}</div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{cls.level}</div>
                              </div>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                              No classes found
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.class_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.class_id}</p>
                  )}
                </div>

                {/* Period Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Period <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.period_id}
                    onChange={(e) => handleInputChange("period_id", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.period_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    } ${!formData.class_id || !formData.day_of_week || isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={!formData.class_id || !formData.day_of_week || isEditing}
                  >
                    <option value="">
                      {formData.class_id && formData.day_of_week ? "Select period" : "Select day and class first"}
                    </option>
                    {classPeriods.length > 0 ? (
                      classPeriods.map((schedule) => {
                        // Handle both populated and non-populated period_id
                        let period;
                        if (typeof schedule.period_id === 'object' && schedule.period_id._id) {
                          // period_id is populated
                          period = schedule.period_id;
                        } else {
                          // period_id is just an ID string, find in periods array
                          period = periods.find(p => p._id === schedule.period_id);
                          // If not found by _id, try to find by period_number if schedule has it
                          if (!period && schedule.period_number) {
                            period = periods.find(p => p.period_number === schedule.period_number);
                          }

                          // If still not found, create a fallback period object
                          if (!period) {
                            period = {
                              _id: schedule.period_id,
                              period_number: schedule.period_number || 'Unknown',
                              start_time: schedule.start_time || '00:00',
                              end_time: schedule.end_time || '00:00'
                            };
                          }
                        }

                        return (
                          <option key={schedule._id} value={schedule._id}>
                            {period ? `Period ${period.period_number} (${period.start_time?.slice(0,5) || period.start_time}-${period.end_time?.slice(0,5) || period.end_time})` : `Unknown Period (${schedule.period_id})`}
                          </option>
                        );
                      })
                    ) : (
                      // Fallback: show all periods if no schedules exist
                      periods.map((period) => (
                        <option key={period._id} value={period._id}>
                          Period {period.period_number} ({period.start_time?.slice(0,5)}-{period.end_time?.slice(0,5)})
                        </option>
                      ))
                    )}
                  </select>
                  {errors.period_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.period_id}</p>
                  )}
                </div>

                {/* Subject Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Subject <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.subject_id}
                    onChange={(e) => handleInputChange("subject_id", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.subject_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    } ${!formData.period_id || !formData.class_id || !formData.day_of_week || isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={!formData.period_id || !formData.class_id || !formData.day_of_week || isEditing}
                  >
                    <option value="">
                      {formData.period_id && formData.class_id && formData.day_of_week ? "Select subject" : "Complete previous selections first"}
                    </option>
                    {periodSubjects.length > 0 ? (
                      periodSubjects.map((subject) => (
                        <option key={subject._id} value={subject._id}>
                          {subject.name}
                        </option>
                      ))
                    ) : (
                      // Fallback: show all subjects if no specific subjects found
                      subjects.map((subject) => (
                        <option key={subject._id} value={subject._id}>
                          {subject.name}
                        </option>
                      ))
                    )}
                  </select>
                  {errors.subject_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                  )}
                </div>
              </div>

              {/* Date and Academic Year */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange("date", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.date 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  />
                  {errors.date && (
                    <p className="mt-1 text-sm text-red-500">{errors.date}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Academic Year <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.academic_year}
                    onChange={(e) => handleInputChange("academic_year", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                      errors.academic_year 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="e.g., 2024-2025"
                  />
                  {errors.academic_year && (
                    <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                  )}
                </div>
              </div>

              {/* Students Attendance Table */}
              {formData.class_id && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                      {isEditing ? "Edit Student Attendance" : `Students Attendance (${studentsToShow.length} students)`}
                    </h4>
                    
                    {/* Bulk Actions - Hide when editing individual attendance */}
                    {!isEditing && (
                      <div className="flex items-center space-x-3">
                        <select
                          value={bulkStatus}
                          onChange={(e) => setBulkStatus(e.target.value as any)}
                          className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
                        >
                          <option value="Present">Present</option>
                          <option value="Absent">Absent</option>
                          <option value="Late">Late</option>
                          <option value="Excused">Excused</option>
                        </select>
                        <button
                          type="button"
                          onClick={applyBulkStatus}
                          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                          disabled={studentsToShow.length === 0}
                        >
                          Apply to All
                        </button>
                      </div>
                    )}
                  </div>

                  {studentsToShow.length > 0 ? (
                    <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                                Student
                              </th>
                              <th className="px-4 py-3 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                Present
                              </th>
                              <th className="px-4 py-3 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                Absent
                              </th>
                              <th className="px-4 py-3 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                Late
                              </th>
                              <th className="px-4 py-3 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                Excused
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                            {studentsToShow.map((student) => (
                              <tr key={student._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td className="px-4 py-3">
                                  <div>
                                    <div className="font-medium text-gray-900 dark:text-white">
                                      {student.first_name} {student.last_name}
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                      {student.student_id}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-4 py-3 text-center">
                                  <input
                                    type="radio"
                                    name={`attendance_${student._id}`}
                                    value="Present"
                                    checked={(studentAttendance[student.student_id] || studentAttendance[student._id]) === "Present"}
                                    onChange={() => handleStudentAttendance(student._id, "Present")}
                                    className="h-4 w-4 text-green-600 focus:ring-green-500"
                                  />
                                </td>
                                <td className="px-4 py-3 text-center">
                                  <input
                                    type="radio"
                                    name={`attendance_${student._id}`}
                                    value="Absent"
                                    checked={(studentAttendance[student._id] || studentAttendance[student.student_id]) === "Absent"}
                                    onChange={() => handleStudentAttendance(student._id, "Absent")}
                                    className="h-4 w-4 text-red-600 focus:ring-red-500"
                                  />
                                </td>
                                <td className="px-4 py-3 text-center">
                                  <input
                                    type="radio"
                                    name={`attendance_${student._id}`}
                                    value="Late"
                                    checked={(studentAttendance[student._id] || studentAttendance[student.student_id]) === "Late"}
                                    onChange={() => handleStudentAttendance(student._id, "Late")}
                                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500"
                                  />
                                </td>
                                <td className="px-4 py-3 text-center">
                                  <input
                                    type="radio"
                                    name={`attendance_${student._id}`}
                                    value="Excused"
                                    checked={(studentAttendance[student._id] || studentAttendance[student.student_id]) === "Excused"}
                                    onChange={() => handleStudentAttendance(student._id, "Excused")}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                  />
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      No students found for this class
                    </div>
                  )}

                  {errors.students && (
                    <p className="mt-2 text-sm text-red-500">{errors.students}</p>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.class_id}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>
                    {isSubmitting
                      ? "Saving..."
                      : (isEditing ? "Update Attendance" : "Mark Attendance")
                    }
                  </span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
