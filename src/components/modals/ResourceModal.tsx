"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Loader2 } from 'lucide-react';
import { ResourceSchema, ResourceCreateSchema, ResourceUpdateSchema } from '@/app/services/ResourcesServices';
import useAuth from '@/app/hooks/useAuth';

interface ResourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ResourceCreateSchema | ResourceUpdateSchema) => Promise<void>;
  resource?: ResourceSchema | null;
  isSubmitting?: boolean;
}

const ResourceModal: React.FC<ResourceModalProps> = ({
  isOpen,
  onClose,
  onSave,
  resource,
  isSubmitting = false
}) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    resource_type: '',
    link: '',
    school_id: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const resourceTypes = [
    'Document',
    'Video',
    'Audio',
    'Image',
    'Link',
    'PDF',
    'Presentation',
    'Spreadsheet',
    'Other'
  ];

  // Initialize form data when modal opens or resource changes
  useEffect(() => {
    if (isOpen) {
      if (resource) {
        // Edit mode
        setFormData({
          name: resource.name || '',
          resource_type: resource.resource_type || '',
          link: resource.link || '',
          school_id: resource.school_id || ''
        });
      } else {
        // Create mode
        setFormData({
          name: '',
          resource_type: '',
          link: '',
          school_id: user?.school_ids?.[0] || ''
        });
      }
      setErrors({});
    }
  }, [isOpen, resource, user]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Resource name is required';
    }

    if (!formData.resource_type.trim()) {
      newErrors.resource_type = 'Resource type is required';
    }

    if (!formData.link.trim()) {
      newErrors.link = 'Resource link is required';
    } else {
      // Basic URL validation
      try {
        new URL(formData.link);
      } catch {
        newErrors.link = 'Please enter a valid URL';
      }
    }

    if (!formData.school_id.trim()) {
      newErrors.school_id = 'School ID is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (resource) {
        // Update mode
        const updateData: ResourceUpdateSchema = {
          _id: resource._id,
          name: formData.name,
          resource_type: formData.resource_type,
          link: formData.link,
          school_id: formData.school_id
        };
        await onSave(updateData);
      } else {
        // Create mode
        const createData: ResourceCreateSchema = {
          name: formData.name,
          resource_type: formData.resource_type,
          link: formData.link,
          school_id: formData.school_id
        };
        await onSave(createData);
      }
    } catch (error) {
      console.error('Error saving resource:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-text">
              {resource ? 'Edit Resource' : 'Add New Resource'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-text" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* Resource Name Field */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-text mb-2">
                Resource Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${
                  errors.name ? 'border-red-500' : 'border-stroke'
                }`}
                placeholder="e.g., Math Tutorial Video"
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* Resource Type Field */}
            <div>
              <label htmlFor="resource_type" className="block text-sm font-medium text-text mb-2">
                Resource Type <span className="text-red-500">*</span>
              </label>
              <select
                id="resource_type"
                value={formData.resource_type}
                onChange={(e) => handleInputChange('resource_type', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${
                  errors.resource_type ? 'border-red-500' : 'border-stroke'
                }`}
                disabled={isSubmitting}
              >
                <option value="">Select resource type</option>
                {resourceTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
              {errors.resource_type && (
                <p className="mt-1 text-sm text-red-500">{errors.resource_type}</p>
              )}
            </div>

            {/* Link Field */}
            <div>
              <label htmlFor="link" className="block text-sm font-medium text-text mb-2">
                Resource Link <span className="text-red-500">*</span>
              </label>
              <input
                type="url"
                id="link"
                value={formData.link}
                onChange={(e) => handleInputChange('link', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${
                  errors.link ? 'border-red-500' : 'border-stroke'
                }`}
                placeholder="https://example.com/resource"
                disabled={isSubmitting}
              />
              {errors.link && (
                <p className="mt-1 text-sm text-red-500">{errors.link}</p>
              )}
            </div>

            {/* School ID Field (hidden, auto-filled) */}
            <input
              type="hidden"
              value={formData.school_id}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-text bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-teal hover:bg-teal-600 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSubmitting ? 'Saving...' : 'Save'}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ResourceModal;
