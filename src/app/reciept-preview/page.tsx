'use client';

import React from 'react';
import { pdf } from '@react-pdf/renderer';
import REceiptPDF from '@/components/utils/RecieptPDF';

const dummyData = {
  student: {
    student_id: 'STU123456',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    class_level: 'Grade 10',
  },
  school: {
    name: 'Greenfield High School',
  },
  paymentItems: [
    { description: 'Tuition Fee - Term 1', amount: 300 },
    { description: 'Library Fee', amount: 20 },
    { description: 'Sports Fee', amount: 15.5 },
  ],
  receiptId: 'RCPT-2025-0001',
  date: new Date(),
  taxRate: 0.05,
};

const PDFDownloadButton = () => {
  const generatePDF = async () => {
    const blob = await pdf(<REceiptPDF {...dummyData} />).toBlob();
    const url = URL.createObjectURL(blob);
    window.open(url); // Or use anchor to download
  };

  return (
    <button
      onClick={generatePDF}
      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
    >
      Download PDF
    </button>
  );
};

export default PDFDownloadButton;
