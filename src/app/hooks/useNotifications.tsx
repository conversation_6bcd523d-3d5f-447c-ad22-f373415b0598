"use client";

import { useState, useEffect, useCallback } from "react";
import {
  getUserNotifications,
  getNotificationStats,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  NotificationData,
  NotificationStats,
  NotificationResponse
} from "../services/NotificationServices";

interface UseNotificationsReturn {
  notifications: NotificationData[];
  unreadCount: number;
  stats: NotificationStats | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  } | null;
  
  // Actions
  fetchNotifications: (params?: {
    page?: number;
    limit?: number;
    unread_only?: boolean;
    category?: string;
  }) => Promise<void>;
  fetchStats: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotificationById: (notificationId: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

export default function useNotifications(): UseNotificationsReturn {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<{
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  } | null>(null);

  // Fetch notifications
  const fetchNotifications = useCallback(async (params?: {
    page?: number;
    limit?: number;
    unread_only?: boolean;
    category?: string;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response: NotificationResponse = await getUserNotifications(params);
      
      if (params?.page && params.page > 1) {
        // Append to existing notifications for pagination
        setNotifications(prev => [...prev, ...response.notifications]);
      } else {
        // Replace notifications for fresh load
        setNotifications(response.notifications);
      }
      
      setUnreadCount(response.unread_count);
      setPagination(response.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch notification statistics
  const fetchStats = useCallback(async () => {
    try {
      const statsData = await getNotificationStats();
      setStats(statsData);
      setUnreadCount(statsData.unread);
    } catch (err) {
      console.error('Error fetching notification stats:', err);
    }
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await markNotificationAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, read: true, read_at: new Date().toISOString() }
            : notification
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      // Update stats if available
      if (stats) {
        setStats(prev => prev ? { ...prev, unread: Math.max(0, prev.unread - 1) } : null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
      console.error('Error marking notification as read:', err);
    }
  }, [stats]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await markAllNotificationsAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ 
          ...notification, 
          read: true, 
          read_at: new Date().toISOString() 
        }))
      );
      
      // Reset unread count
      setUnreadCount(0);
      
      // Update stats if available
      if (stats) {
        setStats(prev => prev ? { ...prev, unread: 0 } : null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
      console.error('Error marking all notifications as read:', err);
    }
  }, [stats]);

  // Delete notification
  const deleteNotificationById = useCallback(async (notificationId: string) => {
    try {
      await deleteNotification(notificationId);
      
      // Find the notification to check if it was unread
      const notificationToDelete = notifications.find(n => n.id === notificationId);
      const wasUnread = notificationToDelete && !notificationToDelete.read;
      
      // Update local state
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
      
      // Update unread count if the deleted notification was unread
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
        
        // Update stats if available
        if (stats) {
          setStats(prev => prev ? { 
            ...prev, 
            total: Math.max(0, prev.total - 1),
            unread: Math.max(0, prev.unread - 1) 
          } : null);
        }
      } else if (stats) {
        setStats(prev => prev ? { 
          ...prev, 
          total: Math.max(0, prev.total - 1)
        } : null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete notification');
      console.error('Error deleting notification:', err);
    }
  }, [notifications, stats]);

  // Refresh notifications (fetch latest)
  const refreshNotifications = useCallback(async () => {
    await Promise.all([
      fetchNotifications({ page: 1, limit: 20 }),
      fetchStats()
    ]);
  }, [fetchNotifications, fetchStats]);

  // Initial load
  useEffect(() => {
    refreshNotifications();
  }, [refreshNotifications]);

  // Auto-refresh every 30 seconds for unread count
  useEffect(() => {
    const interval = setInterval(() => {
      fetchStats();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [fetchStats]);

  return {
    notifications,
    unreadCount,
    stats,
    isLoading,
    error,
    pagination,
    fetchNotifications,
    fetchStats,
    markAsRead,
    markAllAsRead,
    deleteNotificationById,
    refreshNotifications
  };
}
