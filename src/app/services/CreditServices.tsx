import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import {
  CreditSchema,
  CreditCreateSchema,
  CreditUpdateSchema,
} from "../models/CreditModel";

// Get all credits
export async function getCredits(): Promise<CreditSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/get-credits`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credits");
    }

    const data = await response.json();
    return data.map((credit: any) => ({
      _id: credit._id,
      student_id: credit.student_id,
      school_id: credit.school_id,
      academicYear_id: credit.academicYear_id,
      amountPaid: credit.amountPaid,
      paidAt: credit.paidAt,
      createdAt: credit.createdAt,
      updatedAt: credit.updatedAt,
    })) as CreditSchema[];
  } catch (error) {
    console.error("Error fetching credits:", error);
    throw new Error("Failed to fetch credits");
  }
}

// Get single credit by ID
export async function getCreditById(id: string): Promise<CreditSchema> {
  const response = await fetch(`${BASE_API_URL}/credit/get-credit/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch credit");
  }

  return await response.json();
}

// Create a new credit record
export async function createCredit(data: CreditCreateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit/create-credit`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to create credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Update credit by ID
export async function updateCredit(id: string, data: CreditUpdateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit/update-credit/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to update credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Delete a credit record
export async function deleteCredit(id: string) {
  const response = await fetch(`${BASE_API_URL}/credit/delete-credit/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    let message = "Failed to delete credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Delete multiple credits
export async function deleteMultipleCredits(ids: string[]) {
  const response = await fetch(`${BASE_API_URL}/credit/delete-credits`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify({ ids }),
  });

  if (!response.ok) {
    let message = "Failed to delete credits";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Get credits by school ID
export async function getCreditsBySchoolId(schoolId: string): Promise<CreditSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/get-credits-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credits for the school");
    }

    const credits = await response.json();
    return credits.map((credit: any) => ({
      _id: credit._id,
      student_id: credit.student_id,
      school_id: credit.school_id,
      academicYear_id: credit.academicYear_id,
      amountPaid: credit.amountPaid,
      paidAt: credit.paidAt,
      createdAt: credit.createdAt,
      updatedAt: credit.updatedAt,
    })) as CreditSchema[];
  } catch (error) {
    console.error("Error fetching credits by school ID:", error);
    throw new Error("Failed to fetch credits by school ID");
  }
}

// Get total amount paid across all credits
export async function getTotalAmountPaid(): Promise<number> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/total-amount-paid`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch total amount");
    }

    const data = await response.json();
    return data.totalAmountPaid as number;
  } catch (error) {
    console.error("Error fetching total amount:", error);
    throw new Error("Failed to fetch total amount");
  }
}

// Get total amount paid for current month with % change from previous month
export interface TotalAmountChange {
  totalAmount: number;
  percentageChange: number;
}

export async function getTotalAmountChange(): Promise<TotalAmountChange> {
  try {
    const token = getTokenFromCookie("idToken");

    const response = await fetch(`${BASE_API_URL}/credit/total-amount-change`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch total amount change");
    }

    const data = await response.json();
    return {
      totalAmount: data.totalAmount,
      percentageChange: data.percentageChange,
    };
  } catch (error) {
    console.error("Error fetching total amount change:", error);
    throw new Error("Failed to fetch total amount change");
  }
}
