import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Notification interfaces
export interface NotificationData {
  _id: string;
  notification_id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'announcement' | 'reminder' | 'system';
  category: 'academic' | 'financial' | 'attendance' | 'grades' | 'announcement' | 'system' | 'staff' | 'student' | 'parent' | 'payment' | 'schedule';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  read_at?: string;
  action_url?: string;
  action_label?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  sender_id?: string;
  sender_type: 'user' | 'system' | 'automated';
  school_id?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

export interface NotificationResponse {
  notifications: NotificationData[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  unread_count: number;
}

export interface NotificationStats {
  total: number;
  unread: number;
  by_category: Record<string, { total: number; unread: number }>;
}

export interface CreateNotificationData {
  recipient_ids: string[];
  recipient_type?: 'user' | 'school' | 'class' | 'all';
  school_id?: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'announcement' | 'reminder' | 'system';
  category: 'academic' | 'financial' | 'attendance' | 'grades' | 'announcement' | 'system' | 'staff' | 'student' | 'parent' | 'payment' | 'schedule';
  title: string;
  message: string;
  action_url?: string;
  action_label?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  channels?: {
    in_app?: boolean;
    email?: boolean;
    sms?: boolean;
  };
  expires_at?: string;
  scheduled_for?: string;
}

// Get user notifications
export async function getUserNotifications(params?: {
  page?: number;
  limit?: number;
  unread_only?: boolean;
  category?: string;
}): Promise<NotificationResponse> {
  try {
    const token = getTokenFromCookie("idToken");
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.unread_only) queryParams.append('unread_only', params.unread_only.toString());
    if (params?.category) queryParams.append('category', params.category);

    const response = await fetch(`${BASE_API_URL}/notifications/user/notifications?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch notifications");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching notifications:", error);
    throw new Error("Failed to fetch notifications");
  }
}

// Get notification statistics
export async function getNotificationStats(): Promise<NotificationStats> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/notifications/user/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch notification stats");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching notification stats:", error);
    throw new Error("Failed to fetch notification stats");
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string): Promise<void> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/notifications/mark-read/${notificationId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to mark notification as read");
    }
  } catch (error) {
    console.error("Error marking notification as read:", error);
    throw new Error("Failed to mark notification as read");
  }
}

// Mark all notifications as read
export async function markAllNotificationsAsRead(): Promise<void> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/notifications/mark-all-read`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to mark all notifications as read");
    }
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    throw new Error("Failed to mark all notifications as read");
  }
}

// Delete notification
export async function deleteNotification(notificationId: string): Promise<void> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/notifications/delete/${notificationId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to delete notification");
    }
  } catch (error) {
    console.error("Error deleting notification:", error);
    throw new Error("Failed to delete notification");
  }
}

// Create notification (admin only)
export async function createNotification(data: CreateNotificationData): Promise<void> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/notifications/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create notification");
    }
  } catch (error) {
    console.error("Error creating notification:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create notification");
  }
}

// Helper function to get notification icon based on type
export function getNotificationIcon(type: string) {
  switch (type) {
    case 'success':
      return '✅';
    case 'error':
      return '❌';
    case 'warning':
      return '⚠️';
    case 'info':
      return 'ℹ️';
    case 'announcement':
      return '📢';
    case 'reminder':
      return '⏰';
    case 'system':
      return '⚙️';
    default:
      return '🔔';
  }
}

// Helper function to get notification color based on type
export function getNotificationColor(type: string) {
  switch (type) {
    case 'success':
      return 'text-green-600 bg-green-50';
    case 'error':
      return 'text-red-600 bg-red-50';
    case 'warning':
      return 'text-yellow-600 bg-yellow-50';
    case 'info':
      return 'text-blue-600 bg-blue-50';
    case 'announcement':
      return 'text-purple-600 bg-purple-50';
    case 'reminder':
      return 'text-orange-600 bg-orange-50';
    case 'system':
      return 'text-gray-600 bg-gray-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
}

// Helper function to format notification timestamp
export function formatNotificationTime(timestamp: string): string {
  const now = new Date();
  const notificationTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInMinutes < 1440) { // 24 hours
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `${days}d ago`;
  }
}
