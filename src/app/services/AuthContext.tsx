'use client';
import Cookies from "js-cookie";
import { createContext, useEffect, useState, useCallback } from "react";
import { jwtDecode } from "jwt-decode";
import { getCurrentUser, clearCurrentUserCache } from "./UserServices";
import { UserSchema } from "../models/UserModel";
import { isTokenExpired, clearSession } from "../utils/httpInterceptor";
import { useRouter } from "next/navigation";

export const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api"; 



// Context for authentication and user data

interface AuthContextType {
    user: UserSchema | null;
    isAuthenticated: boolean;
    loading: boolean;
    login: (email: string, password: string, rememberMe: boolean, redirectUrl?: string) => Promise<void>;
    logout: () => Promise<void>;
    redirectAfterLogin: string | null;
    setRedirectAfterLogin: (url: string) => void;
    checkAuthStatus: () => Promise<void>;
    forceLogout: () => void;
}

// Create a context for authentication
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Interface for the provider props
interface AuthProviderProps {
    children: React.ReactNode;
}

// composant AuthProvider qui fournit le contexte d'authentification

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [user, setUser] = useState<UserSchema | null>(null);
    const [loading, setLoading] = useState(true);
    const [redirectAfterLogin, setRedirectAfterLogin] = useState<string | null>(null);
    const [authCheckInterval, setAuthCheckInterval] = useState<NodeJS.Timeout | null>(null);

    // Fonction pour forcer la déconnexion
    const forceLogout = useCallback(() => {
        console.warn("Force logout triggered");
        setUser(null);
        clearSession();
        clearCurrentUserCache(); // Vider le cache utilisateur
        if (authCheckInterval) {
            clearInterval(authCheckInterval);
            setAuthCheckInterval(null);
        }
        if (typeof window !== 'undefined') {
            window.location.href = '/login';
        }
    }, [authCheckInterval]);

    // Fonction pour vérifier le statut d'authentification
    const checkAuthStatus = useCallback(async () => {
        try {
            // Vérifier si le token existe
            const token = Cookies.get("idToken");
            if (!token) {
                forceLogout();
                return;
            }

            // Vérifier si le token est expiré
            if (isTokenExpired()) {
                console.warn("Token expired, logging out");
                forceLogout();
                return;
            }

            // Vérifier avec le serveur
            const currentUser = await getCurrentUser();
            if (!currentUser) {
                forceLogout();
                return;
            }

            setUser(currentUser);
        } catch (error) {
            console.error("Auth check failed:", error);
            forceLogout();
        }
    }, [forceLogout]);

    // vérifier si un utilisateur est déja connecté
    useEffect(() => {
        const checkUserLoggedIn = async () => {
            try {
                // Vérifier d'abord si le token existe et n'est pas expiré
                const token = Cookies.get("idToken");
                if (!token || isTokenExpired()) {
                    setUser(null);
                    setLoading(false);
                    return;
                }

                const user: UserSchema | null = await getCurrentUser();
                if (user) {
                    setUser(user);

                    // Démarrer la vérification périodique SEULEMENT si pas déjà démarrée
                    if (!authCheckInterval) {
                        const interval = setInterval(checkAuthStatus, 300000); // Vérifier toutes les 5 minutes (réduit de 1 minute)
                        setAuthCheckInterval(interval);
                    }
                } else {
                    Cookies.remove("idToken");
                    setUser(null);
                }

            } catch (error) {
                console.error("Error verifying token:", error);
                Cookies.remove("idToken");
                setUser(null);
            } finally {
                setLoading(false);
            }
        };

        // Exécuter seulement au montage initial
        checkUserLoggedIn();

        // Cleanup interval on unmount
        return () => {
            if (authCheckInterval) {
                clearInterval(authCheckInterval);
                setAuthCheckInterval(null);
            }
        };
    }, []); // SUPPRESSION des dépendances pour éviter la boucle

    const login = async (email: string, password: string, rememberMe: boolean, redirectUrl?: string) => {
        try {
            const response = await fetch(`${BASE_API_URL}/auth/login`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                }),
            });

            const data = await response.json();
            if (!response.ok) {
                console.error("Login error:", data.message || "Unknown error");
                throw new Error(data.message || "Login failed");
            }

            const { idToken } = data;
            if (!idToken) {
                throw new Error("No idToken received");
            }

            // Stocker le token dans les cookies
            Cookies.set("idToken", idToken, { expires: rememberMe ? 30 : 7 }); // Expire dans 7 jours

            const user: UserSchema | null = await getCurrentUser(); // Vérifier si l'utilisateur est connecté à nouveau après la connexion réussie
            if (user) {
                setUser(user);
            }

            // Si une URL de redirection est fournie, stocke-la
            if (redirectUrl) {
                setRedirectAfterLogin(redirectUrl);
            }
            
        } catch (error) {
            console.error("Login failed:", error);
            throw error;
        }
    };

    const logout = async () => {
        setUser(null);
        clearCurrentUserCache(); // Vider le cache utilisateur
        Cookies.remove("idToken"); // Supprimer le token des cookies
        setRedirectAfterLogin(null); // Réinitialiser l'URL de redirection

        // Nettoyer l'interval
        if (authCheckInterval) {
            clearInterval(authCheckInterval);
            setAuthCheckInterval(null);
        }

        return Promise.resolve();
    };

    const isAuthentiacted = !!user; // Vérifier si l'utilisateur est authentifié

    return (
        <AuthContext.Provider value={{
            user,
            isAuthenticated: isAuthentiacted,
            loading,
            setRedirectAfterLogin,
            redirectAfterLogin,
            login,
            logout,
            checkAuthStatus,
            forceLogout
        }}>
            {children}
        </AuthContext.Provider>
    );
}