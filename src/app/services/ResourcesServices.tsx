import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Interface pour les Resources (différent de SchoolResources)
export interface ResourceSchema extends Record<string, unknown> {
  _id: string;
  resource_id: string;
  school_id?: string;
  name: string;
  resource_type: string;
  link: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ResourceCreateSchema extends Record<string, unknown> {
  school_id?: string;
  name: string;
  resource_type: string;
  link: string;
}

export interface ResourceUpdateSchema extends Record<string, unknown> {
  _id: string;
  school_id?: string;
  name?: string;
  resource_type?: string;
  link?: string;
}

// Get all resources
export async function getResources(): Promise<ResourceSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/resources/get-resources`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch resources");
    }

    const resources = await response.json();
    return resources.map((resource: any) => ({
      _id: resource._id,
      resource_id: resource.resource_id,
      school_id: resource.school_id,
      name: resource.name,
      resource_type: resource.resource_type,
      link: resource.link,
      createdAt: resource.createdAt,
      updatedAt: resource.updatedAt,
    })) as ResourceSchema[];
  } catch (error) {
    console.error("Error fetching resources:", error);
    throw new Error("Failed to fetch resources");
  }
}

// Get a single resource by ID
export async function getResourceById(resourceId: string): Promise<ResourceSchema> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/resources/get-resource/${resourceId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch resource");
  }

  const resource = await response.json();
  return {
    _id: resource._id,
    resource_id: resource.resource_id,
    school_id: resource.school_id,
    name: resource.name,
    resource_type: resource.resource_type,
    link: resource.link,
    createdAt: resource.createdAt,
    updatedAt: resource.updatedAt,
  };
}

// Create a new resource
export async function createResource(resourceData: ResourceCreateSchema): Promise<ResourceSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/resources/create-resource`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(resourceData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create resource");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating resource:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create resource");
  }
}

// Update a resource (using resource_id)
export async function updateResource(resourceId: string, resourceData: ResourceUpdateSchema): Promise<ResourceSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/resources/update-resource/${resourceId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(resourceData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update resource");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating resource:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update resource");
  }
}

// Delete a resource (using resource_id)
export async function deleteResource(resourceId: string): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/resources/delete-resource/${resourceId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete resource");
    }
  } catch (error) {
    console.error("Error deleting resource:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete resource");
  }
}

// Delete multiple resources
export async function deleteMultipleResources(resourceIds: string[]): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/resources/delete-multiple-resources`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify({ ids: resourceIds }),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete resources");
    }
  } catch (error) {
    console.error("Error deleting multiple resources:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete resources");
  }
}

// Get resources by school ID (filter client-side since backend doesn't have this endpoint)
export async function getResourcesBySchoolId(schoolId: string): Promise<ResourceSchema[]> {
  try {
    const allResources = await getResources();
    return allResources.filter(resource => resource.school_id === schoolId);
  } catch (error) {
    console.error("Error fetching resources by school ID:", error);
    throw new Error("Failed to fetch resources by school ID");
  }
}
