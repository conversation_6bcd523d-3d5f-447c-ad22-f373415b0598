import { TermSchema, CreateTermData, UpdateTermData } from "../models/Term";
import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

// Get all terms for a school
export async function getTermsBySchool(schoolId: string, academicYear?: string): Promise<{
  terms: TermSchema[];
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    let url = `${BASE_API_URL}/terms/school/${schoolId}`;
    if (academicYear) {
      url += `?academic_year=${encodeURIComponent(academicYear)}`;
    }

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching terms:", response.statusText);
      throw new Error("Failed to fetch terms");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch terms error:", error);
    throw new Error("Failed to fetch terms");
  }
}

// Get current term for a school
export async function getCurrentTerm(schoolId: string): Promise<{ term: TermSchema; message: string }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/terms/school/${schoolId}/current`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching current term:", response.statusText);
      throw new Error("Failed to fetch current term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch current term error:", error);
    throw new Error("Failed to fetch current term");
  }
}

// Create a new term
export async function createTerm(schoolId: string, termData: CreateTermData): Promise<{
  term: TermSchema;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/terms/school/${schoolId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(termData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating term:", errorData);
      throw new Error(errorData.message || "Failed to create term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create term error:", error);
    throw error;
  }
}

// Update a term
export async function updateTerm(schoolId: string, termId: string, termData: UpdateTermData): Promise<{
  term: TermSchema;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/terms/school/${schoolId}/${termId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(termData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating term:", errorData);
      throw new Error(errorData.message || "Failed to update term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update term error:", error);
    throw error;
  }
}

// Delete a term (soft delete)
export async function deleteTerm(schoolId: string, termId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/terms/school/${schoolId}/${termId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting term:", errorData);
      throw new Error(errorData.message || "Failed to delete term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete term error:", error);
    throw error;
  }
}

// Set current term
export async function setCurrentTerm(schoolId: string, termId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/terms/school/${schoolId}/${termId}/set-current`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error setting current term:", errorData);
      throw new Error(errorData.message || "Failed to set current term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Set current term error:", error);
    throw error;
  }
}
