import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { DisciplineSchema, DisciplineCreateSchema, DisciplineUpdateSchema } from "../models/Discipline";

// Get all disciplines
export async function getDisciplines(): Promise<DisciplineSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/discipline/get-discipline`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch disciplines");
    }

    const disciplines = await response.json();
    return disciplines.map((discipline: any) => ({
      _id: discipline._id,
      discipline_id: discipline.discipline_id,
      school_id: discipline.school_id,
      student_id: discipline.student_id,
      comments: discipline.comments,
      createdAt: discipline.createdAt,
      updatedAt: discipline.updatedAt,
    })) as DisciplineSchema[];
  } catch (error) {
    console.error("Error fetching disciplines:", error);
    throw new Error("Failed to fetch disciplines");
  }
}

// Get a single discipline by ID
export async function getDisciplineById(disciplineId: string): Promise<DisciplineSchema> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/discipline/get-discipline/${disciplineId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch discipline");
  }

  const discipline = await response.json();
  return {
    _id: discipline._id,
    discipline_id: discipline.discipline_id,
    school_id: discipline.school_id,
    student_id: discipline.student_id,
    comments: discipline.comments,
    createdAt: discipline.createdAt,
    updatedAt: discipline.updatedAt,
  };
}

// Create a new discipline
export async function createDiscipline(disciplineData: DisciplineCreateSchema): Promise<DisciplineSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/discipline/create-discipline`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(disciplineData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create discipline");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating discipline:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create discipline");
  }
}

// Update a discipline
export async function updateDiscipline(disciplineId: string, disciplineData: DisciplineUpdateSchema): Promise<DisciplineSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/discipline/update-discipline/${disciplineId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(disciplineData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update discipline");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating discipline:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update discipline");
  }
}

// Delete a discipline
export async function deleteDiscipline(disciplineId: string): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/discipline/delete-discipline/${disciplineId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete discipline");
    }
  } catch (error) {
    console.error("Error deleting discipline:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete discipline");
  }
}

// Delete multiple disciplines
export async function deleteMultipleDisciplines(disciplineIds: string[]): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/discipline/delete-multiple-disciplines`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify({ ids: disciplineIds }),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete disciplines");
    }
  } catch (error) {
    console.error("Error deleting multiple disciplines:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete disciplines");
  }
}
