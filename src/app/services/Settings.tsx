import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { 
  SettingsSchema, 
  SettingsCreateSchema, 
  SettingsUpdateSchema 
} from "../models/Settings";

// Get the current settings (assuming there is only one settings document)
export async function getSettings(): Promise<SettingsSchema> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/settings/get-settings`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch settings");
    }

    const settings = await response.json();
    return settings as SettingsSchema;
  } catch (error) {
    console.error("Error fetching settings:", error);
    throw new Error("Failed to fetch settings");
  }
}

// Create new settings
export async function createSettings(settingsData: SettingsCreateSchema) {
  try {
    const response = await fetch(`${BASE_API_URL}/settings/create-settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(settingsData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create settings");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating settings:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create settings");
  }
}

// Update settings by ID
export async function updateSettings(settingsId: string, settingsData: SettingsUpdateSchema) {
  try {
    const response = await fetch(`${BASE_API_URL}/settings/update-settings/${settingsId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(settingsData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update settings");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating settings:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update settings");
  }
}

// Delete settings by ID
export async function deleteSettings(settingsId: string) {
  try {
    const response = await fetch(`${BASE_API_URL}/settings/delete-settings/${settingsId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete settings");
    }

    return await response.json();
  } catch (error) {
    console.error("Error deleting settings:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete settings");
  }
}
export async function patchSettingsSection(section: string, data: Partial<SettingsSchema>) {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/settings/patch-settings/${section}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to patch settings section");
    }

    return await response.json();
  } catch (error) {
    console.error("Error patching settings section:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to patch settings section");
  }
}
