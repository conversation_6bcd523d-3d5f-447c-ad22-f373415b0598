import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Interface for staff permissions (matching backend StaffPermission model)
export interface StaffPermissions {
  students: {
    view_all_students: boolean;
    add_edit_delete_students: boolean;
    generate_id_cards: boolean;
    generate_report_cards: boolean;
  };
  academic_records: {
    view_grades_assigned_classes: boolean;
    enter_edit_grades_assigned_classes: boolean;
    view_all_school_grades: boolean;
    take_attendance_assigned_classes: boolean;
    view_all_attendance: boolean;
    manage_terms: boolean;
    manage_timetables: boolean;
    manage_periods: boolean;
    manage_subjects: boolean;
    manage_classes: boolean;
    manage_exam_types: boolean;
    manage_discipline: boolean;
  };
  staff: {
    view_staff_list: boolean;
    add_edit_delete_staff: boolean;
    manage_staff_permissions: boolean;
    reset_staff_passwords: boolean;
    manage_teacher_assignments: boolean;
  };
  financials: {
    view_student_fee_balances: boolean;
    record_fee_payments: boolean;
    manage_school_credit_balance: boolean;
    view_financial_reports: boolean;
    manage_fee_types: boolean;
    view_transactions: boolean;
  };
  classes: {
    view_all_classes: boolean;
    add_edit_delete_classes: boolean;
    manage_class_schedules: boolean;
    assign_teachers_to_classes: boolean;
  };
  announcements: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
  };
  resources: {
    view_resources: boolean;
    add_edit_delete_resources: boolean;
    manage_resource_categories: boolean;
  };
  reports: {
    generate_student_reports: boolean;
    generate_financial_reports: boolean;
    generate_attendance_reports: boolean;
    export_data: boolean;
  };
  communications: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
    manage_resources: boolean;
  };
  school_management: {
    view_school_info: boolean;
    edit_school_info: boolean;
    manage_school_settings: boolean;
  };
}

export interface StaffPermissionResponse {
  _id: string;
  permission_id: string;
  user_id: string;
  school_id: string;
  role_template: string;
  permissions: StaffPermissions;
  assigned_classes: Array<{
    class_id: string;
    subjects: string[];
    periods: string[];
  }>;
  is_active: boolean;
  granted_by: string;
  granted_at: string;
  last_modified_by?: string;
  last_modified_at?: string;
  createdAt: string;
  updatedAt: string;
}

// Get staff permissions for a user in a specific school
export async function getStaffPermissions(userId: string, schoolId: string): Promise<StaffPermissionResponse | null> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/user/${userId}/school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // No permissions found
      }
      throw new Error("Failed to fetch staff permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching staff permissions:", error);
    throw new Error("Failed to fetch staff permissions");
  }
}

// Get current user's staff permissions for a specific school
export async function getCurrentUserStaffPermissions(schoolId: string): Promise<StaffPermissionResponse | null> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/current-user/school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // No permissions found
      }
      throw new Error("Failed to fetch current user staff permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching current user staff permissions:", error);
    throw new Error("Failed to fetch current user staff permissions");
  }
}

// Create staff permissions
export async function createStaffPermissions(data: {
  user_id: string;
  school_id: string;
  role_template: string;
  permissions: StaffPermissions;
  assigned_classes?: Array<{
    class_id: string;
    subjects: string[];
    periods: string[];
  }>;
}): Promise<StaffPermissionResponse> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create staff permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating staff permissions:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create staff permissions");
  }
}

// Update staff permissions
export async function updateStaffPermissions(permissionId: string, data: {
  role_template?: string;
  permissions?: StaffPermissions;
  assigned_classes?: Array<{
    class_id: string;
    subjects: string[];
    periods: string[];
  }>;
  is_active?: boolean;
}): Promise<StaffPermissionResponse> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/update/${permissionId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update staff permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating staff permissions:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update staff permissions");
  }
}

// Delete staff permissions
export async function deleteStaffPermissions(permissionId: string): Promise<void> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/delete/${permissionId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete staff permissions");
    }
  } catch (error) {
    console.error("Error deleting staff permissions:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete staff permissions");
  }
}

// Get all staff permissions for a school
export async function getSchoolStaffPermissions(schoolId: string): Promise<StaffPermissionResponse[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch school staff permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching school staff permissions:", error);
    throw new Error("Failed to fetch school staff permissions");
  }
}

// Get default permissions for a role template
export async function getDefaultPermissionsForRole(roleTemplate: string): Promise<StaffPermissions> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/staff/permissions/default-permissions/${roleTemplate}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch default permissions");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching default permissions:", error);
    throw new Error("Failed to fetch default permissions");
  }
}
