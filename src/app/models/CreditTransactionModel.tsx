// creditTransaction.model.ts

export interface CreditTransactionSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;          // MongoDB ObjectId as string
  academicYear_id: string;    // MongoDB ObjectId as string
  payment_method: 'manual' | 'automatic' | 'gift';
  amountPaid: number;
  credit: number;
  paidAt?: string;            // ISO date string, optional because default is Date.now
  createdAt?: string;         // Auto-generated timestamp
  updatedAt?: string;         // Auto-generated timestamp
}

export interface CreditTransactionCreateSchema extends Record<string, unknown> {
  school_id: string;          // Required
  academicYear_id: string;    // Required
  payment_method: 'manual' | 'automatic' | 'gift';  // Required
  amountPaid: number;         // Required
  credit: number;             // Required
  paidAt?: string;            // Optional, can be auto-assigned server side
}

export interface CreditTransactionUpdateSchema extends Record<string, unknown> {
  _id: string;               // Required to identify which transaction to update
  school_id?: string;
  academicYear_id?: string;
  payment_method?: 'manual' | 'automatic' | 'gift';
  amountPaid?: number;
  credit?: number;
  paidAt?: string;
}

export interface CreditTransactionDeleteSchema extends Record<string, unknown> {
  _id: string;               // Required MongoDB ID to delete
}
