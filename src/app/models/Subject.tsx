export interface SubjectSchema extends Record<string, unknown> {
  _id: string;
  subject_id: string;
  subject_code: string;
  compulsory: boolean;
  school_id: string;           // MongoDB ObjectId as string
  class_id: string[];            // MongoDB ObjectId as string
  name: string;
  description: string;
  coefficient: number;
  department?: string;
  createdAt?: string;          // Auto-generated timestamp
  updatedAt?: string;          // Auto-generated timestamp
}

export interface SubjectCreateSchema extends Record<string, unknown> {
  subject_code: string;        // Required
  compulsory: boolean;         // Required
  school_id: string;           // Required
  class_id: string[];            // Required
  name: string;                // Required
  description: string;         // Required
  coefficient: number;         // Required
  department?: string;         // Optional
}

export interface SubjectUpdateSchema extends Record<string, unknown> {
  _id: string;                 // Required for identifying the subject
  subject_id?: string;
  subject_code?: string;
  compulsory?: boolean;
  school_id?: string;
  class_id?: string[];
  name?: string;
  description?: string;
  coefficient?: number;
  department?: string;
}

export interface SubjectDeleteSchema extends Record<string, unknown> {
  _id: string;                 // Required MongoDB ID to delete
}
