export interface GradeSchema extends Record<string, unknown> {
  _id: string;
  school_id?: string;           // Optional MongoDB ObjectId as string (reference to School)
  subject_id: string;           // Required (reference to Subject)
  student_id: string;           // Required (reference to Student)
  exam_type: string;            // Required (reference to ExamType)
  term: 'First Term' | 'Second Term' | 'Third Term';  // Required term enum
  academic_year: string;        // Required academic year (e.g., "2024-2025")
  grade?: string;               // Optional grade (e.g., "A", "B+", etc.)
  score: number;                // Required numerical score
  comments?: string;            // Optional teacher comments
  createdAt?: string;           // Auto-generated timestamp
  updatedAt?: string;           // Auto-generated timestamp
}

export interface GradeCreateSchema extends Record<string, unknown> {
  subject_id: string;           // Required
  student_id: string;           // Required
  exam_type: string;            // Required
  term: 'First Term' | 'Second Term' | 'Third Term';  // Required
  academic_year: string;        // Required
  score: number;                // Required
  grade?: string;               // Optional
  school_id?: string;           // Optional
  comments?: string;            // Optional
}

export interface GradeUpdateSchema extends Record<string, unknown> {
  _id: string;                  // Required to identify grade record
  subject_id?: string;
  student_id?: string;
  exam_type?: string;
  term?: 'First Term' | 'Second Term' | 'Third Term';
  academic_year?: string;
  score?: number;
  grade?: string;
  school_id?: string;
  comments?: string;
}

export interface GradeDeleteSchema extends Record<string, unknown> {
  _id: string;                  // Required MongoDB ID to delete
}
