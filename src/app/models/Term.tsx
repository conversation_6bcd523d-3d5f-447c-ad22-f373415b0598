export interface SequenceSchema {
  sequence_number: number;
  sequence_name: string;
}

export interface TermSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  name: string;
  term_number: number;
  sequences: SequenceSchema[];
  academic_year: string;
  start_date: string;
  end_date: string;
  is_current: boolean;
  is_active: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateTermData {
  name: string;
  term_number: number;
  sequences: SequenceSchema[];
  academic_year: string;
  start_date: string;
  end_date: string;
  is_current?: boolean;
}

export interface UpdateTermData {
  name?: string;
  term_number?: number;
  sequences?: SequenceSchema[];
  academic_year?: string;
  start_date?: string;
  end_date?: string;
  is_current?: boolean;
  is_active?: boolean;
}
