// settings.model.ts

export interface SettingsSchema extends Record<string, unknown> {
  _id: string;  // MongoDB ObjectId as string

  general: {
    platform_name: string;
    support_email: string;
    default_language: string;
    maintenance_mode: boolean;
    maintenance_message?: string;
  };

  financials: {
    price_per_credit: number;
    payment_gateway: string;
    late_payment_fee: number;
    payment_due_period: number; // in days
  };

  notifications: {
    methods: {
      sms: boolean;
      email: boolean;
      in_app: boolean;
    };
    notification_frequency: 'instant' | 'daily' | 'weekly';
    system_announcement_banner: {
      enabled: boolean;
      text?: string;
      banner_type: 'info' | 'warning' | 'error' | 'success';
    };
  };

  security: {
    password_min_length: number;
    password_require_special_char: boolean;
    session_timeout_minutes: number;
    two_factor_auth: boolean;
    login_attempt_limit: number;
    lockout_duration_minutes: number;
  };

  createdAt?: string;  // ISO timestamp
  updatedAt?: string;  // ISO timestamp
}

export interface SettingsCreateSchema extends Record<string, unknown> {
  general: {
    platform_name: string;
    support_email: string;
    default_language?: string;
    maintenance_mode?: boolean;
    maintenance_message?: string;
  };

  financials: {
    price_per_credit: number;
    payment_gateway: string;
    late_payment_fee: number;
    payment_due_period: number;
  };

  notifications: {
    methods?: {
      sms?: boolean;
      email?: boolean;
      in_app?: boolean;
    };
    notification_frequency?: 'instant' | 'daily' | 'weekly';
    system_announcement_banner?: {
      enabled?: boolean;
      text?: string;
      banner_type?: 'info' | 'warning' | 'error' | 'success';
    };
  };

  security?: {
    password_min_length?: number;
    password_require_special_char?: boolean;
    session_timeout_minutes?: number;
    two_factor_auth?: boolean;
    login_attempt_limit?: number;
    lockout_duration_minutes?: number;
  };
}

export interface SettingsUpdateSchema extends Partial<SettingsCreateSchema> {
  _id: string;
}

export interface SettingsDeleteSchema extends Record<string, unknown> {
  _id: string;
}
