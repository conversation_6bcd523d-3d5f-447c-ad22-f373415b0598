export interface PeriodSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;           // MongoDB ObjectId as string
  period_number: number;
  start_time: string;          // Time string in HH:mm:ss format
  end_time: string;            // Time string in HH:mm:ss format
  createdAt?: string;          // Auto-generated timestamp
  updatedAt?: string;          // Auto-generated timestamp
}

export interface PeriodCreateSchema extends Record<string, unknown> {
  school_id: string;           // Required
  period_number: number;       // Required
  start_time: string;          // Required
  end_time: string;            // Required
}

export interface PeriodUpdateSchema extends Record<string, unknown> {
  _id: string;                 // Required for identifying the period
  school_id?: string;
  period_number?: number;
  start_time?: string;
  end_time?: string;
}

export interface PeriodDeleteSchema extends Record<string, unknown> {
  _id: string;                 // Required MongoDB ID to delete
}
