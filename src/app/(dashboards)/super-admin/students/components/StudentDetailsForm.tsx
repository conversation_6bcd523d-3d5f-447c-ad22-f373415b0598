import React, { useState } from "react";
import AsyncSelect from "react-select/async";
import { components } from "react-select";
import CustomInput from "../../../../../components/inputs/CustomInput";
import CustomSelect from "../../../../../components/inputs/CustomSelect";
import CustomDateInput from "../../../../../components/inputs/CustomDateInput";
import CustomNationalitySelect from "../../../../../components/inputs/CustomNationalitySelect";
import { searchStudents } from "@/app/services/StudentServices";
import { backIn } from "framer-motion";

interface StudentDetailsFormProps {
  formData: {
    firstName: string;
    lastName: string;
    middleName?: string;
    dateOfBirth?: string;
    nationality?: string;
    gender?: string;
    place_of_birth?: string;
    [key: string]: any;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  schoolId: string;
}

const StudentDetailsForm: React.FC<StudentDetailsFormProps> = ({ formData, handleChange, schoolId }) => {
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  const customSelectStyles = {
 control: (provided: any, state: any) => ({
      ...provided,
      borderColor: state.isFocused ? '#14b8a6' : '#d1d5db', // teal ring vs gray-300
      boxShadow: state.isFocused ? '0 0 0 2px #14b8a6' : 'none',
      padding: '2px 8px',
      borderRadius: '0.375rem', // rounded-md = 6px
      minHeight: '38px',
      backgroundColor: state.isDisabled ? '#f3f4f6' : 'bg-gray-100', // bg-gray-100
      color: state.isDisabled ? '#9ca3af' : 'inherit',
    }),
    input: (provided: any) => ({
      ...provided,
      margin: 0,
      padding: 0,
      color: 'inherit',
      fontSize: '0.875rem', // text-sm
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: '#4b5563', // gray-600
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: 'inherit',
    }),
    dropdownIndicator: (provided: any, state: any) => ({
      ...provided,
      color: state.isFocused ? '#14b8a6' : '#9ca3af',
      padding: 4,
    }),
    clearIndicator: (provided: any) => ({
      ...provided,
      padding: 4,
    }),
    menu: (provided: any) => ({
      ...provided,
      borderRadius: '0.375rem',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isFocused ? '#14b8a6' : 'bg-background',
      color: state.isFocused ? 'white' : 'black',
      padding: '8px 12px',
      cursor: 'pointer',
    }),
  };

  const loadOptions = async (inputValue: string) => {
    if (!inputValue) return [];
    try {
      const students = await searchStudents(schoolId, { name: inputValue });

      return students.map((student: any) => ({
        label: `${student.first_name} ${student.last_name} | DOB: ${student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : "N/A"} | ID: ${student.student_id || "N/A"}`,
        value: student._id,
        data: {
          firstName: student.first_name,
          lastName: student.last_name,
          middleName: student.middle_name,
          dateOfBirth: student.date_of_birth,
          nationality: student.nationality,
          gender: student.gender,
          place_of_birth: student.place_of_birth,
        },
      }));
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  const handleStudentSelect = (option: any) => {
    setSelectedStudent(option);
    if (option?.data) {
      const s = option.data;

      handleChange({ target: { name: "firstName", value: s.firstName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "lastName", value: s.lastName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "middleName", value: s.middleName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "dateOfBirth", value: s.dateOfBirth ? s.dateOfBirth.substring(0, 10) : "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "nationality", value: s.nationality || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "gender", value: s.gender || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "place_of_birth", value: s.place_of_birth || "" } } as React.ChangeEvent<HTMLInputElement>);
    } else {
      const fields = ["firstName", "lastName", "middleName", "dateOfBirth", "nationality", "gender", "place_of_birth"];
      fields.forEach(field =>
        handleChange({ target: { name: field, value: "" } } as React.ChangeEvent<HTMLInputElement>)
      );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (selectedStudent) setSelectedStudent(null);
    handleChange(e);
  };

  return (
    <>
      <div className="col-span-2 mb-4">
        <label className="block text-sm font-medium mb-1">Search Student</label>
        <AsyncSelect
          cacheOptions
          loadOptions={loadOptions}
          onChange={handleStudentSelect}
          placeholder="Type student name or ID..."
          value={selectedStudent}
          isClearable
          styles={customSelectStyles}
          components={{
            Input: (props) => (
              <components.Input {...props} autoComplete="off" aria-autocomplete="none" />
            ),
          }}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <CustomInput
          label="First Name"
          id="firstName"
          name="firstName"
          value={formData.firstName}
          onChange={handleInputChange}
          required
        />

        <CustomInput
          label="Last Name"
          id="lastName"
          name="lastName"
          value={formData.lastName}
          onChange={handleInputChange}
          required
        />

        <CustomInput
          label="Middle Name"
          id="middleName"
          placeholder="(Optional)"
          name="middleName"
          value={formData.middleName || ""}
          onChange={handleInputChange}
        />

        <CustomDateInput
          label="Date of Birth"
          id="dateOfBirth"
          name="dateOfBirth"
          value={formData.dateOfBirth || ""}
          onChange={handleInputChange}
          required
          
        />

        <CustomNationalitySelect
          label="Nationality"
          id="nationality"
          name="nationality"
          value={formData.nationality || ""}
          onChange={handleInputChange}
          required
        />

        <CustomSelect
          label="Gender"
          id="gender"
          name="gender"
          value={formData.gender || ""}
          onChange={handleInputChange}
          required
          options={[
            { label: "Male", value: "Male" },
            { label: "Female", value: "Female" },
          ]}
          placeholder="Select gender"
        />

        <CustomInput
          label="Place Of Birth"
          id="place_of_birth"
          name="place_of_birth"
          value={formData.place_of_birth || ""}
          onChange={handleInputChange}
          required
        />
      </div>
    </>
  );
};

export default StudentDetailsForm;
