"use client";

import {
  DollarSign,
  GraduationCap,
  LayoutDashboard,
  School,
  UserIcon,
} from "lucide-react";
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import StatsOverview from "@/components/widgets/StatsOverview";
import AChart from "@/components/utils/AChart";
import PerformanceTable from "@/components/utils/PerformanceTable";
import useAuth from "@/app/hooks/useAuth";
import { useEffect, useState } from "react";
import CircularLoader from "@/components/widgets/CircularLoader";

import {
  getTotalAmountChange,
  getTotalAmountPaid,
} from "@/app/services/CreditServices";
import {
  getTotalStudents,
  getStudentsCountChange,
} from "@/app/services/StudentServices";
import {
  getTotalSchools,
  getSchoolCountChange,
  getSchoolPerformance,
} from "@/app/services/SchoolServices";
import {
  getTotalUsers,
  getUserCountWithChange,
} from "@/app/services/UserServices";

const BASE_URL = "/super-admin";

const navigation = {
  icon: LayoutDashboard,
  baseHref: `${BASE_URL}/dashboard`,
  title: "Dashboard",
};

export default function Page() {
  const { logout } = useAuth();

  const [revenueData, setRevenueData] = useState<{
    totalAmount: number;
    percentageChange: number;
  } | null>(null);

  const [totalAmountPaid, setTotalAmountPaid] = useState<number | null>(null);
  const [totalStudents, setTotalStudents] = useState<number | null>(null);
  const [studentsChange, setStudentsChange] = useState<number>(0);
  const [totalSchools, setTotalSchools] = useState<number | null>(null);
  const [schoolsChange, setSchoolsChange] = useState<number>(0);
  const [totalUsers, setTotalUsers] = useState<number | null>(null);
  const [usersChange, setUsersChange] = useState<number>(0);
  const [performanceData, setPerformanceData] = useState<any[]>([]);
  const [metricOptions, setMetricOptions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchDashboardData() {
      try {
        const [
          revenue,
          totalPaid,
          totalStudentsResponse,
          { percentageChange: studentPctChange },
          totalSchoolsCount,
          { percentageChange: schoolPctChange },
          totalUsersCount,
          { percentageChange: userPctChange },
          performanceResponse,
        ] = await Promise.all([
          getTotalAmountChange(),
          getTotalAmountPaid(),
          getTotalStudents(),
          getStudentsCountChange(),
          getTotalSchools(),
          getSchoolCountChange(),
          getTotalUsers(),
          getUserCountWithChange(),
          getSchoolPerformance(),
        ]);

        setRevenueData(revenue);
        setTotalAmountPaid(totalPaid);
        setTotalStudents(totalStudentsResponse.totalStudents);
        setStudentsChange(studentPctChange);
        setTotalSchools(totalSchoolsCount);
        setSchoolsChange(schoolPctChange);
        setTotalUsers(totalUsersCount);
        setUsersChange(userPctChange);
        setPerformanceData(performanceResponse);

        // Extract unique metric names from performance data
        const metricsSet = new Set<string>();
        performanceResponse?.forEach((entry: any) => {
          if (entry.metrics && typeof entry.metrics === "object") {
            Object.keys(entry.metrics).forEach((key) => metricsSet.add(key));
          }
        });
        setMetricOptions(Array.from(metricsSet));
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardData();
  }, []);

  return (
    <SuperLayout
      navigation={navigation}
      showGoPro={true}
      onLogout={() => logout()}
    >
      <div className="flex flex-col gap-3">
        {loading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <CircularLoader />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
              <StatsOverview
                value={
                  totalAmountPaid ? totalAmountPaid.toLocaleString() : "0"
                }
                changePercentage={revenueData?.percentageChange || 0}
                title="Total Revenue (XAF)"
                icon={<DollarSign />}
              />
              <StatsOverview
                value={totalSchools?.toLocaleString() || "0"}
                changePercentage={schoolsChange}
                title="Total Schools"
                icon={<School />}
              />
              <StatsOverview
                value={totalStudents?.toLocaleString() || "0"}
                changePercentage={studentsChange}
                title="Total Students"
                icon={<GraduationCap />}
              />
              <StatsOverview
                value={totalUsers?.toLocaleString() || "0"}
                changePercentage={usersChange}
                title="Total Users"
                icon={<UserIcon />}
              />
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
              <div className="rounded-lg border bg-widget border-gray-300 darK:border dark:border-gray-800 p-4 h-max">
                <AChart />
              </div>
              <PerformanceTable
                data={performanceData}
                defaultItemsPerPage={5}
                metricOptions={metricOptions}
              />
            </div>
          </>
        )}
      </div>
    </SuperLayout>
  );
}
