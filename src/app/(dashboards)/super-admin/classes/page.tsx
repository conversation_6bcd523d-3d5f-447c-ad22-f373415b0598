"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState, useMemo } from 'react'; // Added useMemo
import { Presentation, Search, MapPin, Building2, MoveRight } from 'lucide-react'; // Added more icons
import { useRouter } from 'next/navigation';
import { SchoolSchema } from '@/app/models/SchoolModel';
import { getSchools } from '@/app/services/SchoolServices';
import Link from 'next/link';
// Removed DataTableFix as we're now using a card layout
// import DataTableFix from '@/components/utils/TableFix';
import { getClasses } from '@/app/services/ClassServices';
import { ClassSchema } from '@/app/models/ClassModel';


export default function Page() {
    const BASE_URL = "/super-admin";

    const navigation = {
        icon: Presentation, // Icon for the main navigation item
        baseHref: `${BASE_URL}/classes`,
        title: "Classes",
    };

    function Classes() {
        const router = useRouter();
        const [classes, setClasses] = useState<ClassSchema[]>([]);
        const [schools, setSchools] = useState<SchoolSchema[]>([]);
        const [loadingData, setLoadingData] = useState(false);
        const [searchTerm, setSearchTerm] = useState(''); // State for search term

        // Fetch both schools and classes data on component mount
        useEffect(() => {
            const fetchData = async () => {
                setLoadingData(true);
                try {
                    const [fetchedClasses, fetchedSchools] = await Promise.all([
                        getClasses(),
                        getSchools(),
                    ]);
                    setClasses(fetchedClasses);
                    setSchools(fetchedSchools);
                } catch (error) {
                    console.error("Error fetching schools or classes:", error);
                    // TODO: Implement user-friendly error display (e.g., a toast message)
                } finally {
                    setLoadingData(false);
                }
            };
            fetchData();
        }, []); // Empty dependency array means this effect runs once on mount

        // Memoized filtering of schools based on the search term
        const filteredSchools = useMemo(() => {
            const lowercasedSearchTerm = searchTerm.toLowerCase();
            return schools.filter(school =>
                school.name.toLowerCase().includes(lowercasedSearchTerm) ||
                (school.address?.toLowerCase().includes(lowercasedSearchTerm) ?? false)
            );
        }, [schools, searchTerm]); // Recalculate only when schools or searchTerm change

        // Function to count classes for a given school ID
        const getClassCount = (schoolId: string) => {
            return classes.filter(cls => cls.school_id === schoolId).length;
        };

        return (
            <div className="p-6">
                {/* Search Bar for filtering schools */}
                <div className="mb-6 relative">
                    <input
                        type="text"
                        placeholder="Search schools by name or address..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>

                {/* Conditional rendering based on loading state and search results */}
                {loadingData ? (
                    // Display loading skeletons while data is being fetched
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {[...Array(6)].map((_, index) => (
                            <div key={index} className="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6 animate-pulse">
                                <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-6"></div>
                                <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                            </div>
                        ))}
                    </div>
                ) : filteredSchools.length === 0 ? (
                    // Display a message if no schools are found after filtering
                    <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        <p>No schools found matching your search criteria.</p>
                    </div>
                ) : (
                    // Display school cards in a responsive grid
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredSchools.map((school) => (
                            <div
                                key={school._id}
                                className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 flex flex-col justify-between transition-transform transform hover:scale-105"
                            >
                                <div>
                                    {/* School Name with Building icon and link */}
                                    <h3 className="text-xl font-semibold mb-2 text-foreground flex items-center">
                                        <Building2 className="mr-2 text-teal-500" size={24} />
                                        <Link href={`${BASE_URL}/schools/view?id=${school._id}`} className="hover:text-teal-600 transition-colors">
                                            {school.name}
                                        </Link>
                                    </h3>
                                    {/* School Address with MapPin icon */}
                                    <p className="text-gray-600 dark:text-gray-300 mb-2 gap-2 flex items-center">
                                        <MapPin className="text-gray-400" size={18} />
                                        {school.address}
                                    </p>
                                    {/* Classes Count with Presentation icon */}
                                    <p className="text-gray-600 dark:text-gray-300 mb-4 flex gap-2 items-center">
                                        <Presentation className="text-teal" size={18} /> {/* Changed icon to Presentation */}
                                        <span className="font-medium">{getClassCount(school._id)}</span> Classes
                                    </p>
                                </div>
                                <div className="mt-4">
                                    {/* Manage Classes button with BookText icon */}
                                    <button
                                        onClick={() => router.push(`${BASE_URL}/classes/manage?id=${school._id}`)}
                                        className="w-full gap-2 px-4 py-2 text-teal rounded-md transition-colors flex items-center justify-center"
                                    >
                                        Manage Classes
                                        <MoveRight className="mr-2" size={18} /> {/* Changed icon to BookText */}

                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    }

    return (
        <Suspense fallback={
            <div className="flex justify-center items-center h-screen w-screen fixed top-0 left-0 bg-gray-50 dark:bg-gray-900 z-50">
                <CircularLoader size={32} color="teal" />
            </div>
        }>
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <Classes />
            </SuperLayout>
        </Suspense>
    );
}