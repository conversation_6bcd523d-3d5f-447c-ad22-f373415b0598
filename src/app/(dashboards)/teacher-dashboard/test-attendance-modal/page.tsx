"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import TeacherAttendanceModal from "@/components/modals/TeacherAttendanceModal";
import { getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

// Mock data for testing
const mockClassInfo = {
  _id: "class123",
  name: "6ème A",
  level: "6ème",
  section: "A"
};

const mockSubjects = [
  { _id: "math123", name: "Mathematics" },
  { _id: "eng123", name: "English" },
  { _id: "fr123", name: "French" },
  { _id: "sci123", name: "<PERSON>" },
  { _id: "hist123", name: "History" }
];

const mockPeriods = [
  { _id: "p1", period_number: 1, start_time: "08:00", end_time: "09:00" },
  { _id: "p2", period_number: 2, start_time: "09:00", end_time: "10:00" },
  { _id: "p3", period_number: 3, start_time: "10:30", end_time: "11:30" },
  { _id: "p4", period_number: 4, start_time: "11:30", end_time: "12:30" },
  { _id: "p5", period_number: 5, start_time: "14:00", end_time: "15:00" },
  { _id: "p6", period_number: 6, start_time: "15:00", end_time: "16:00" }
];

const mockSchedules = [
  // Monday
  { _id: "s1", day_of_week: "Monday", subject_id: "math123", period_id: "p1", class_id: "class123" },
  { _id: "s2", day_of_week: "Monday", subject_id: "eng123", period_id: "p2", class_id: "class123" },
  { _id: "s3", day_of_week: "Monday", subject_id: "fr123", period_id: "p3", class_id: "class123" },
  
  // Tuesday
  { _id: "s4", day_of_week: "Tuesday", subject_id: "math123", period_id: "p1", class_id: "class123" },
  { _id: "s5", day_of_week: "Tuesday", subject_id: "math123", period_id: "p2", class_id: "class123" }, // Math has 2 periods
  { _id: "s6", day_of_week: "Tuesday", subject_id: "sci123", period_id: "p3", class_id: "class123" },
  { _id: "s7", day_of_week: "Tuesday", subject_id: "hist123", period_id: "p4", class_id: "class123" },
  
  // Wednesday
  { _id: "s8", day_of_week: "Wednesday", subject_id: "eng123", period_id: "p1", class_id: "class123" },
  { _id: "s9", day_of_week: "Wednesday", subject_id: "sci123", period_id: "p2", class_id: "class123" },
  { _id: "s10", day_of_week: "Wednesday", subject_id: "sci123", period_id: "p3", class_id: "class123" }, // Science has 2 periods
  
  // Thursday
  { _id: "s11", day_of_week: "Thursday", subject_id: "fr123", period_id: "p1", class_id: "class123" },
  { _id: "s12", day_of_week: "Thursday", subject_id: "hist123", period_id: "p2", class_id: "class123" },
  { _id: "s13", day_of_week: "Thursday", subject_id: "math123", period_id: "p3", class_id: "class123" },
  
  // Friday
  { _id: "s14", day_of_week: "Friday", subject_id: "eng123", period_id: "p1", class_id: "class123" },
  { _id: "s15", day_of_week: "Friday", subject_id: "fr123", period_id: "p2", class_id: "class123" },
  { _id: "s16", day_of_week: "Friday", subject_id: "sci123", period_id: "p5", class_id: "class123" },
  { _id: "s17", day_of_week: "Friday", subject_id: "hist123", period_id: "p6", class_id: "class123" }
];

const mockStudents = [
  { _id: "st1", first_name: "Jean", last_name: "Dupont", roll_number: "001", class_id: "class123" },
  { _id: "st2", first_name: "Marie", last_name: "Martin", roll_number: "002", class_id: "class123" },
  { _id: "st3", first_name: "Pierre", last_name: "Bernard", roll_number: "003", class_id: "class123" },
  { _id: "st4", first_name: "Sophie", last_name: "Dubois", roll_number: "004", class_id: "class123" },
  { _id: "st5", first_name: "Lucas", last_name: "Moreau", roll_number: "005", class_id: "class123" }
];

export default function TestAttendanceModalPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [navigation, setNavigation] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [attendanceToEdit, setAttendanceToEdit] = useState<any>(null);

  useEffect(() => {
    if (user) {
      // Get selected school from localStorage
      const storedSchool = localStorage.getItem("teacher_selected_school");
      if (storedSchool) {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
      } else {
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  // Set basic navigation for test page
  useEffect(() => {
    const testNavigation = [
      {
        title: "Test Tools",
        icon: "TestTube",
        items: [
          { icon: "Modal", name: "Test Modal", href: "/teacher-dashboard/test-attendance-modal" },
          { icon: "ClipboardList", name: "Back to Attendance", href: "/teacher-dashboard/attendance" },
          { icon: "Home", name: "Dashboard", href: "/teacher-dashboard/dashboard" }
        ]
      }
    ];
    setNavigation(testNavigation);
  }, []);

  const handleOpenModal = () => {
    setAttendanceToEdit(null);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = () => {
    // Mock existing attendance for editing
    const mockAttendance = {
      _id: "att123",
      day_of_week: "Monday",
      subject_id: "math123",
      period_id: "s1", // This is the schedule ID
      date: new Date().toISOString().split('T')[0],
      academic_year: "2024-2025",
      students: [
        { student_id: "st1", status: "Present" },
        { student_id: "st2", status: "Absent" },
        { student_id: "st3", status: "Present" }
      ]
    };
    setAttendanceToEdit(mockAttendance);
    setIsModalOpen(true);
  };

  const handleAttendanceSubmit = async (data: any) => {
    console.log("📝 Attendance submitted:", data);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    alert("Attendance saved successfully! Check console for details.");
    setIsModalOpen(false);
  };

  const getScheduleAnalysis = () => {
    const analysis: any = {};
    
    mockSchedules.forEach(schedule => {
      if (!analysis[schedule.day_of_week]) {
        analysis[schedule.day_of_week] = {};
      }
      
      const subject = mockSubjects.find(s => s._id === schedule.subject_id);
      const period = mockPeriods.find(p => p._id === schedule.period_id);
      
      if (!analysis[schedule.day_of_week][subject?.name || 'Unknown']) {
        analysis[schedule.day_of_week][subject?.name || 'Unknown'] = [];
      }
      
      analysis[schedule.day_of_week][subject?.name || 'Unknown'].push({
        period: period?.period_number || 0,
        time: period ? `${period.start_time}-${period.end_time}` : 'Unknown'
      });
    });
    
    return analysis;
  };

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Test Attendance Modal</h1>
              <p className="text-foreground/60">
                Test the improved attendance modal with subject-period filtering
              </p>
            </div>
            <button
              onClick={() => router.push("/teacher-dashboard/attendance")}
              className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
            >
              Back to Attendance
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Test Controls */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-foreground mb-4">Test Controls</h2>
              
              <div className="space-y-4">
                <button
                  onClick={handleOpenModal}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Test New Attendance Modal
                </button>
                
                <button
                  onClick={handleOpenEditModal}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Test Edit Attendance Modal
                </button>
              </div>

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Test Features</h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Select day → subjects filter automatically</li>
                  <li>• Select subject → periods filter for that subject</li>
                  <li>• Multiple periods per subject on same day</li>
                  <li>• Visual indicators for dependencies</li>
                  <li>• Automatic field clearing on parent changes</li>
                </ul>
              </div>
            </div>

            {/* Schedule Analysis */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-foreground mb-4">Mock Schedule Analysis</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {Object.entries(getScheduleAnalysis()).map(([day, subjects]: [string, any]) => (
                  <div key={day} className="border border-gray-200 dark:border-gray-700 rounded p-3">
                    <h3 className="font-medium text-foreground mb-2">{day}</h3>
                    <div className="space-y-1">
                      {Object.entries(subjects).map(([subject, periods]: [string, any]) => (
                        <div key={subject} className="text-sm">
                          <span className="font-medium text-foreground/80">{subject}:</span>
                          <span className="ml-2 text-foreground/60">
                            {periods.map((p: any) => `P${p.period} (${p.time})`).join(', ')}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Modal */}
          <TeacherAttendanceModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              setAttendanceToEdit(null);
            }}
            onSubmit={handleAttendanceSubmit}
            attendance={attendanceToEdit}
            isEditing={!!attendanceToEdit}
            classInfo={mockClassInfo}
            subjects={mockSubjects}
            periods={mockPeriods}
            schedules={mockSchedules}
            students={mockStudents}
            loading={false}
          />
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
