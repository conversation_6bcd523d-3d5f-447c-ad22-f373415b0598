"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { BookOpen, Users, Calendar, Clock, ArrowLeft, GraduationCap } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface ClassDetail {
  class_id: string;
  class_name: string;
  level: string;
  subjects: string[];
  students: Array<{
    _id: string;
    first_name: string;
    last_name: string;
    email: string;
    student_id?: string;
  }>;
  schedule: Array<{
    day: string;
    time: string;
    subject: string;
    period: string;
  }>;
  grades: Array<{
    student_id: string;
    student_name: string;
    subject: string;
    grade: string;
    score: number;
  }>;
}

// Skeleton component for loading state
const ClassDetailSkeleton = () => (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          <div className="flex-1">
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
                  </div>
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              </div>
          ))}
        </div>
      </div>

      {/* Content Grid Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
              <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-1/4 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
        ))}
      </div>
    </div>
);

const navigation = {
  icon: BookOpen,
  baseHref: "/teacher-dashboard/classes",
  title: "Class Details"
};

export default function ClassDetailPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const params = useParams();
  const classId = params.classId as string;

  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [classDetail, setClassDetail] = useState<ClassDetail | null>(null);
  const [teacherData, setTeacherData] = useState<any>(null);
  const [navigationItems, setNavigation] = useState<any[]>([]);

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        // Load class detail when school is set
        fetchClassDetail(school.school_id, classId);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      // No school selected, redirect to school selection
      router.push("/teacher-dashboard");
    }
  }, [user, router, classId]);

  const fetchClassDetail = async (schoolId: string, classId: string) => {
    try {
      setLoading(true);

      const { getTeacherPermissions, getTeacherStudents, getTeacherSchedule } = await import("@/app/services/TeacherPermissionServices");
      const [teacherData, studentsData, scheduleData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherStudents(schoolId),
        getTeacherSchedule(schoolId)
      ]);

      // Set navigation and store teacher data
      setNavigation(getTeacherNavigationItems(teacherData.permissions));
      setTeacherData(teacherData);

      // Find the specific class
      const classInfo = teacherData.assigned_classes.find(cls => cls._id === classId);
      if (!classInfo) {
        router.push("/teacher-dashboard/classes");
        return;
      }

      // Get students for this class
      const classStudents = studentsData.filter(student => student.class_id === classId);

      // Get subjects for this class
      let classSubjects = teacherData.assigned_subjects
          .filter(subject => subject.class_id === classId)
          .map(subject => subject.name);

      // If no subjects found in assigned_subjects, try to get from schedule
      if (classSubjects.length === 0) {
        const classScheduleSubjects = scheduleData
          .filter(s => s.class_id === classId)
          .map(s => s.subject);
        classSubjects = [...new Set(classScheduleSubjects)];
        console.log(`Found subjects from schedule for class ${classInfo.name}:`, classSubjects);
      }

      console.log(`Class ${classInfo.name} final subjects:`, classSubjects);

      // TODO: Fetch real schedule and grades data
      const mockSchedule = [
        { day: "Monday", time: "08:00-09:00", subject: classSubjects[0] || "Math", period: "1" },
        { day: "Tuesday", time: "10:00-11:00", subject: classSubjects[1] || "Science", period: "3" },
        { day: "Wednesday", time: "14:00-15:00", subject: classSubjects[0] || "Math", period: "6" },
      ];

      const mockGrades = classStudents.slice(0, 3).map(student => ({
        student_id: student._id,
        student_name: `${student.first_name} ${student.last_name}`,
        subject: classSubjects[0] || "Math",
        grade: "A",
        score: 85
      }));

      setClassDetail({
        class_id: classInfo._id,
        class_name: classInfo.name,
        level: classInfo.level,
        subjects: classSubjects,
        students: classStudents,
        schedule: mockSchedule,
        grades: mockGrades
      });

    } catch (error) {
      console.error("Error fetching class detail:", error);
      router.push("/teacher-dashboard/classes");
    } finally {
      setLoading(false);
    }
  };
  console.log("teacherData ", teacherData)
  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      "Mathematics": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      "Science": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      "English": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      "History": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      "Geography": "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300",
      "Physics": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      "Chemistry": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      "Biology": "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300",
    };
    return colors[subject as keyof typeof colors] || "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  };

  if (loading) {
    return (
        <ProtectedRoute allowedRoles={["teacher"]}>
          <TeacherLayout
              navigation={navigation}
              selectedSchool={selectedSchool ? {
                _id: selectedSchool.school_id,
                name: selectedSchool.school_name
              } : null}
              onSchoolChange={handleSchoolChange}
              onLogout={logout}
          >
            <ClassDetailSkeleton />
          </TeacherLayout>
        </ProtectedRoute>
    );
  }

  if (!classDetail) {
    return (
        <ProtectedRoute allowedRoles={["teacher"]}>
          <TeacherLayout
              navigation={navigation}
              selectedSchool={selectedSchool ? {
                _id: selectedSchool.school_id,
                name: selectedSchool.school_name
              } : null}
              onSchoolChange={handleSchoolChange}
              onLogout={logout}
          >
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium text-foreground mb-2">Class Not Found</h3>
                <p className="text-foreground/60 mb-4">The requested class could not be found.</p>
                <button
                    onClick={() => router.push("/teacher-dashboard/classes")}
                    className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                >
                  Back to Classes
                </button>
              </div>
            </div>
          </TeacherLayout>
        </ProtectedRoute>
    );
  }

  return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
            navigation={navigation}
            selectedSchool={selectedSchool ? {
              _id: selectedSchool.school_id,
              name: selectedSchool.school_name
            } : null}
            onSchoolChange={handleSchoolChange}
            onLogout={logout}
        >
          <div className="space-y-6">
            {/* Header */}
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <button
                      onClick={() => router.push("/teacher-dashboard/classes")}
                      className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <ArrowLeft className="h-5 w-5 text-foreground/70" />
                  </button>
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-foreground">{classDetail.class_name}</h1>
                    <p className="text-foreground/60">
                      {classDetail.level} • {selectedSchool?.school_name}
                    </p>
                  </div>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600 dark:text-blue-400">Total Students</p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{classDetail.students.length}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600 dark:text-green-400">Subjects</p>
                      <p className="text-2xl font-bold text-green-700 dark:text-green-300">{classDetail.subjects.length}</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-green-500" />
                  </div>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-orange-600 dark:text-orange-400">Weekly Classes</p>
                      <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{classDetail.schedule.length}</p>
                    </div>
                    <Calendar className="h-8 w-8 text-orange-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Subjects */}
              <div className="bg-widget rounded-lg border border-stroke p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Subjects</h3>
                {classDetail.subjects.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {classDetail.subjects.map((subject) => (
                          <span
                              key={subject}
                              className={`px-3 py-1 rounded-full text-sm font-medium ${getSubjectColor(subject)}`}
                          >
                      {subject}
                    </span>
                      ))}
                    </div>
                ) : (
                    <p className="text-foreground/60">No subjects assigned yet.</p>
                )}
              </div>

              {/* Schedule */}
              <div className="bg-widget rounded-lg border border-stroke p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Weekly Schedule</h3>
                {classDetail.schedule.length > 0 ? (
                    <div className="space-y-3">
                      {classDetail.schedule.map((item, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <Clock className="h-4 w-4 text-foreground/60" />
                              <div>
                                <p className="font-medium text-foreground">{item.subject}</p>
                                <p className="text-sm text-foreground/60">{item.day}</p>
                              </div>
                            </div>
                            <span className="text-sm text-foreground/70">{item.time}</span>
                          </div>
                      ))}
                    </div>
                ) : (
                    <p className="text-foreground/60">No schedule available.</p>
                )}
              </div>

              {/* Students */}
              <div className="bg-widget rounded-lg border border-stroke p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Students ({classDetail.students.length})</h3>
                {classDetail.students.length > 0 ? (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {classDetail.students.map((student) => (
                          <div key={student._id} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {student.first_name.charAt(0)}{student.last_name.charAt(0)}
                        </span>
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-foreground">
                                {student.first_name} {student.last_name}
                              </p>
                              <p className="text-sm text-foreground/60">{student.email}</p>
                            </div>
                          </div>
                      ))}
                    </div>
                ) : (
                    <p className="text-foreground/60">No students enrolled yet.</p>
                )}
              </div>

              {/* Recent Grades */}
              <div className="bg-widget rounded-lg border border-stroke p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Recent Grades</h3>
                {classDetail.grades.length > 0 ? (
                    <div className="space-y-3">
                      {classDetail.grades.map((grade, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div>
                              <p className="font-medium text-foreground">{grade.student_name}</p>
                              <p className="text-sm text-foreground/60">{grade.subject}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-foreground">{grade.grade}</p>
                              <p className="text-sm text-foreground/60">{grade.score}%</p>
                            </div>
                          </div>
                      ))}
                    </div>
                ) : (
                    <p className="text-foreground/60">No grades recorded yet.</p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            {teacherData && (
              <div className="flex flex-wrap gap-4">
                {/* Manage Grades - Check grades permissions */}
                {(teacherData.permissions?.academic_records?.view_grades_assigned_classes || teacherData.permissions?.academic_records?.enter_edit_grades_assigned_classes) && (
                  <button
                    onClick={() => router.push(`/teacher-dashboard/grades?class=${classDetail.class_id}`)}
                    className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                  >
                    <GraduationCap className="h-4 w-4" />
                    <span>Manage Grades</span>
                  </button>
                )}

                {/* Take Attendance - Check attendance permissions */}
                {(teacherData.permissions?.academic_records?.take_attendance_assigned_classes ) && (
                  <button
                    onClick={() => router.push(`/teacher-dashboard/attendance?class=${classDetail.class_id}`)}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                  >
                    <Users className="h-4 w-4" />
                    <span>Take Attendance</span>
                  </button>
                )}

                {/* View Timetable - Check timetable permissions */}
                {teacherData.permissions?.classes?.manage_class_schedules && (
                  <button
                    onClick={() => router.push("/teacher-dashboard/timetable")}
                    className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>View Timetable</span>
                  </button>
                )}

                {/* Always show back to classes button */}
                <button
                  onClick={() => router.push("/teacher-dashboard/classes")}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Classes</span>
                </button>
              </div>
            )}
          </div>
        </TeacherLayout>
      </ProtectedRoute>
  );
}