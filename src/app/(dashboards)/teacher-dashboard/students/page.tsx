"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { GraduationCap, Users, BookOpen, Mail, Phone } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import TeacherStudentsSkeleton from "@/components/skeletons/TeacherStudentsSkeleton";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface Student {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  class_id: string;
  class_name: string;
  student_id?: string;
}

interface ClassGroup {
  class_id: string;
  class_name: string;
  students: Student[];
}

interface Student {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  class_id: string;
  class_name: string;
  student_id?: string;
}

// Skeleton component for loading state
const StudentSkeleton = () => (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-1"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
        </div>
      </div>
    </div>
);

const navigation = {
  icon: GraduationCap,
  baseHref: "/teacher-dashboard/students",
  title: "My Students"
};

export default function TeacherStudentsPage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState<Student[]>([]);
  const [classGroups, setClassGroups] = useState<ClassGroup[]>([]);
  const [stats, setStats] = useState({
    totalStudents: 0,
    activeClasses: 0,
    attendanceRate: 0
  });

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        // Load students data when school is set
        loadStudentsData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      // No school selected, redirect to school selection
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  const loadStudentsData = async (schoolId: string) => {
    try {
      setLoading(true);
      // Import services dynamically to avoid SSR issues
      const { getTeacherStudents, getTeacherPermissions } = await import("@/app/services/TeacherPermissionServices");

      // Get students and teacher data
      const [studentsData, teacherData] = await Promise.all([
        getTeacherStudents(schoolId),
        getTeacherPermissions(schoolId)
      ]);

      console.log("Students data loaded:", studentsData);
      console.log("Teacher data loaded:", teacherData);

      setStudents(studentsData);

      // Group students by class and sort alphabetically
      const groupedByClass = studentsData.reduce((acc: { [key: string]: Student[] }, student: Student) => {
        if (!acc[student.class_id]) {
          acc[student.class_id] = [];
        }
        acc[student.class_id].push(student);
        return acc;
      }, {});

      // Create class groups with sorted students
      const classGroupsData: ClassGroup[] = Object.keys(groupedByClass).map(classId => {
        const classStudents = groupedByClass[classId];
        // Sort students alphabetically by first name, then last name
        const sortedStudents = classStudents.sort((a, b) => {
          const nameA = `${a.first_name} ${a.last_name}`.toLowerCase();
          const nameB = `${b.first_name} ${b.last_name}`.toLowerCase();
          return nameA.localeCompare(nameB);
        });

        return {
          class_id: classId,
          class_name: classStudents[0]?.class_name || 'Unknown Class',
          students: sortedStudents
        };
      });

      // Sort class groups alphabetically by class name
      classGroupsData.sort((a, b) => a.class_name.localeCompare(b.class_name));
      setClassGroups(classGroupsData);

      // Calculate stats
      const totalStudents = studentsData.length;
      const activeClasses = teacherData.assigned_classes.length;

      // TODO: Calculate actual attendance rate from attendance records
      const attendanceRate = totalStudents > 0 ? 85 : 0; // Placeholder

      setStats({
        totalStudents,
        activeClasses,
        attendanceRate
      });

    } catch (error) {
      console.error("Error loading students data:", error);
      setStudents([]);
      setClassGroups([]);
      setStats({ totalStudents: 0, activeClasses: 0, attendanceRate: 0 });
    } finally {
      setLoading(false);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const getClassColor = (index: number) => {
    const colors = [
      "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300",
      "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300",
    ];
    return colors[index % colors.length];
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherStudentsSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
            navigation={navigation}
            selectedSchool={selectedSchool ? {
              _id: selectedSchool.school_id,
              name: selectedSchool.school_name
            } : null}
            onSchoolChange={handleSchoolChange}
            onLogout={logout}
        >
          <div className="space-y-6">
            {/* Header */}
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">My Students</h1>
                  <p className="text-foreground/60">
                    Manage and view your students at {selectedSchool?.school_name}
                  </p>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600 dark:text-blue-400">Total Students</p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{stats.totalStudents}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600 dark:text-green-400">Active Classes</p>
                      <p className="text-2xl font-bold text-green-700 dark:text-green-300">{stats.activeClasses}</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-green-500" />
                  </div>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-orange-600 dark:text-orange-400">Classes Teaching</p>
                      <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{classGroups.length}</p>
                    </div>
                    <GraduationCap className="h-8 w-8 text-orange-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Students Content */}
            <div className="bg-widget rounded-lg border border-stroke p-6">
              {classGroups.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">No Students Found</h3>
                  <p className="text-foreground/60 mb-6">
                    No students are assigned to your classes yet.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
                    <button
                      onClick={() => router.push("/teacher-dashboard/classes")}
                      className="flex items-center justify-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                    >
                      <BookOpen size={16} />
                      <span>View Classes</span>
                    </button>

                    <button
                      onClick={() => router.push("/teacher-dashboard/dashboard")}
                      className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                      <span>Back to Dashboard</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-8">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-foreground">Students by Class</h2>
                    <p className="text-sm text-foreground/60">
                      {stats.totalStudents} students across {classGroups.length} classes
                    </p>
                  </div>

                  {classGroups.map((classGroup, index) => (
                    <div key={classGroup.class_id} className="space-y-4">
                      {/* Class Header */}
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-bold">
                            {classGroup.class_name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-foreground">{classGroup.class_name}</h3>
                          <p className="text-sm text-foreground/60">
                            {classGroup.students.length} student{classGroup.students.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getClassColor(index)}`}>
                          Class {index + 1}
                        </span>
                      </div>

                      {/* Students Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ml-11">
                        {classGroup.students.map((student) => (
                          <div key={student._id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center space-x-3 mb-3">
                              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span className="text-white font-medium text-sm">
                                  {student.first_name?.charAt(0)}{student.last_name?.charAt(0)}
                                </span>
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium text-foreground">
                                  {student.first_name} {student.last_name}
                                </h4>
                                <p className="text-xs text-foreground/50">{student.class_name}</p>
                              </div>
                            </div>

                            <div className="space-y-2 text-sm">
                              {student.student_id && (
                                <p className="text-foreground/70">
                                  <span className="font-medium">ID:</span> {student.student_id}
                                </p>
                              )}
                              {student.email && (
                                <div className="flex items-center space-x-2 text-foreground/70">
                                  <Mail className="h-4 w-4 flex-shrink-0" />
                                  <span className="truncate">{student.email}</span>
                                </div>
                              )}
                              {student.phone && (
                                <div className="flex items-center space-x-2 text-foreground/70">
                                  <Phone className="h-4 w-4 flex-shrink-0" />
                                  <span>{student.phone}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </TeacherLayout>
      </ProtectedRoute>
  );
}