"use client";

import React, { useState, useEffect } from "react";
import { X, Calendar, Save, Plus, Trash2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { TermSchema, CreateTermData, UpdateTermData, SequenceSchema } from "@/app/models/Term";

interface TermModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateTermData | UpdateTermData) => Promise<void>;
  term?: TermSchema | null;
  loading?: boolean;
}

export default function TermModal({
  isOpen,
  onClose,
  onSubmit,
  term,
  loading = false
}: TermModalProps) {
  const [formData, setFormData] = useState<CreateTermData>({
    name: "",
    term_number: 1,
    sequences: [
      { sequence_number: 1, sequence_name: "1ère Séquence" },
      { sequence_number: 2, sequence_name: "2ème Séquence" }
    ],
    academic_year: new Date().getFullYear() + "-" + (new Date().getFullYear() + 1),
    start_date: "",
    end_date: "",
    is_current: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when term prop changes
  useEffect(() => {
    if (term) {
      setFormData({
        name: term.name,
        term_number: term.term_number,
        sequences: term.sequences,
        academic_year: term.academic_year,
        start_date: term.start_date.split('T')[0], // Convert to YYYY-MM-DD format
        end_date: term.end_date.split('T')[0],
        is_current: term.is_current
      });
    } else {
      // Reset form for new term
      setFormData({
        name: "",
        term_number: 1,
        sequences: [
          { sequence_number: 1, sequence_name: "1ère Séquence" },
          { sequence_number: 2, sequence_name: "2ème Séquence" }
        ],
        academic_year: new Date().getFullYear() + "-" + (new Date().getFullYear() + 1),
        start_date: "",
        end_date: "",
        is_current: false
      });
    }
    setErrors({});
  }, [term, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Term name is required";
    }

    if (!formData.start_date) {
      newErrors.start_date = "Start date is required";
    }

    if (!formData.end_date) {
      newErrors.end_date = "End date is required";
    }

    if (formData.start_date && formData.end_date && formData.start_date >= formData.end_date) {
      newErrors.end_date = "End date must be after start date";
    }

    if (formData.sequences.length === 0) {
      newErrors.sequences = "At least one sequence is required";
    }

    // Validate sequences
    formData.sequences.forEach((seq, index) => {
      if (!seq.sequence_name.trim()) {
        newErrors[`sequence_${index}`] = "Sequence name is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting term:", error);
    }
  };

  const handleInputChange = (field: keyof CreateTermData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const addSequence = () => {
    const newSequenceNumber = formData.sequences.length + 1;
    const newSequence: SequenceSchema = {
      sequence_number: newSequenceNumber,
      sequence_name: `${newSequenceNumber}${newSequenceNumber === 1 ? 'ère' : 'ème'} Séquence`
    };
    
    setFormData(prev => ({
      ...prev,
      sequences: [...prev.sequences, newSequence]
    }));
  };

  const removeSequence = (index: number) => {
    if (formData.sequences.length > 1) {
      const newSequences = formData.sequences.filter((_, i) => i !== index);
      // Renumber sequences
      const renumberedSequences = newSequences.map((seq, i) => ({
        ...seq,
        sequence_number: i + 1
      }));
      
      setFormData(prev => ({
        ...prev,
        sequences: renumberedSequences
      }));
    }
  };

  const updateSequence = (index: number, field: keyof SequenceSchema, value: string | number) => {
    const newSequences = [...formData.sequences];
    newSequences[index] = { ...newSequences[index], [field]: value };
    
    setFormData(prev => ({
      ...prev,
      sequences: newSequences
    }));

    // Clear sequence error
    if (errors[`sequence_${index}`]) {
      setErrors(prev => ({ ...prev, [`sequence_${index}`]: "" }));
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-teal/10 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-teal" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-foreground">
                  {term ? "Edit Term" : "Create New Term"}
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {term ? "Update term information" : "Add a new academic term"}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Term Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Premier Trimestre"
                  className={`w-full px-3 py-2 border rounded-lg bg-widget text-foreground placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent ${
                    errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Term Number *
                </label>
                <select
                  value={formData.term_number}
                  onChange={(e) => handleInputChange("term_number", parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  <option value={1}>Term 1</option>
                  <option value={2}>Term 2</option>
                  <option value={3}>Term 3</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Academic Year *
                </label>
                <input
                  type="text"
                  value={formData.academic_year}
                  onChange={(e) => handleInputChange("academic_year", e.target.value)}
                  placeholder="e.g., 2024-2025"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                />
              </div>

              <div className="flex items-center">
                <label className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <input
                    type="checkbox"
                    checked={formData.is_current}
                    onChange={(e) => handleInputChange("is_current", e.target.checked)}
                    className="w-4 h-4 text-teal bg-gray-100 border-gray-300 rounded focus:ring-teal dark:focus:ring-teal dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  Set as Current Term
                </label>
              </div>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Start Date *
                </label>
                <input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange("start_date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent ${
                    errors.start_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                {errors.start_date && <p className="text-red-500 text-xs mt-1">{errors.start_date}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  End Date *
                </label>
                <input
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange("end_date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent ${
                    errors.end_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                {errors.end_date && <p className="text-red-500 text-xs mt-1">{errors.end_date}</p>}
              </div>
            </div>

            {/* Sequences */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-foreground">
                  Sequences *
                </label>
                <button
                  type="button"
                  onClick={addSequence}
                  className="flex items-center gap-1 text-teal hover:text-teal-dark text-sm font-medium"
                >
                  <Plus className="w-4 h-4" />
                  Add Sequence
                </button>
              </div>

              <div className="space-y-3">
                {formData.sequences.map((sequence, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={sequence.sequence_name}
                        onChange={(e) => updateSequence(index, "sequence_name", e.target.value)}
                        placeholder="Sequence name"
                        className={`w-full px-3 py-2 border rounded-lg bg-widget text-foreground placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent ${
                          errors[`sequence_${index}`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors[`sequence_${index}`] && (
                        <p className="text-red-500 text-xs mt-1">{errors[`sequence_${index}`]}</p>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 min-w-[80px]">
                      Seq. {sequence.sequence_number}
                    </div>
                    {formData.sequences.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeSequence(index)}
                        className="p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
              {errors.sequences && <p className="text-red-500 text-xs mt-1">{errors.sequences}</p>}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-stroke">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                Cancel
              </button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={loading}
                className="bg-teal text-white px-6 py-2 rounded-lg flex items-center gap-2 hover:bg-teal-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {term ? "Update Term" : "Create Term"}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
