"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Percent, ArrowLeft, Plus, Filter, Download, TrendingUp, Users, BookOpen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import DataTableFix from "@/components/utils/TableFix";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SchoolAdminGradesSkeleton } from "@/components/skeletons";
import {
  getGradeRecords,
  getGradeStats,
  createGrade,
  updateGrade,
  deleteGrade,
  deleteMultipleGrades,
  exportGradesPDF,
  exportGradesExcel,
  getAvailableTerms,
  GradeRecord,
  GradeStats,
  GradeTerm,
  GradeSequence
} from "@/app/services/GradeServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getSubjectById } from "@/app/services/SubjectServices";
import { getExamTypes } from "@/app/services/ExamTypeServices";
import { getClassById } from "@/app/services/ClassServices";
import { verifyPassword } from "@/app/services/UserServices";
import GradeModal from "@/components/modals/GradeModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import {useRouter, useParams, useSearchParams} from "next/navigation";
import Link from "next/link";

export default function SubjectGradesPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId_combine = searchParams.get("classId") as string;
  const subjectId = searchParams.get("subjectId") as string;

  const classId = classId_combine?.split("__")[0] as string;
  const class_id = classId_combine?.split("__")[1] as string;

  // State management
  const [gradeRecords, setGradeRecords] = useState<GradeRecord[]>([]);
  const [stats, setStats] = useState<GradeStats>({
    totalGrades: 0,
    averageScore: 0,
    highestScore: 0,
    lowestScore: 0,
    passRate: 0
  });
  const [classData, setClassData] = useState<any>(null);
  const [subjectData, setSubjectData] = useState<any>(null);
  const [students, setStudents] = useState<any[]>([]);
  const [examTypes, setExamTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [gradeToEdit, setGradeToEdit] = useState<GradeRecord | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedGrades, setSelectedGrades] = useState<GradeRecord[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<GradeRecord | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [clearSelection, setClearSelection] = useState(false);

  // Filters - Enhanced with terms and sequences
  const [selectedTerm, setSelectedTerm] = useState<string>('all');
  const [selectedSequence, setSelectedSequence] = useState<string>('all');
  const [selectedExamType, setSelectedExamType] = useState<string>('all');

  // Terms data
  const [availableTerms, setAvailableTerms] = useState<GradeTerm[]>([]);
  const [currentTerm, setCurrentTerm] = useState<GradeTerm | null>(null);
  const [loadingTerms, setLoadingTerms] = useState(false);

  const schoolId:any = user?.school_ids?.[0] || user?.school_id;

  const navigation = {
    icon: Percent,
    baseHref: `/school-admin/grades/class/${classId}/subject/${subjectId}`,
    title: `${classData?.name} - ${subjectData?.name} Grades`
  };

  // Load available terms
  useEffect(() => {
    const loadTerms = async () => {
      if (!schoolId) return;

      try {
        setLoadingTerms(true);
        const termsData = await getAvailableTerms(schoolId);
        setAvailableTerms(termsData.terms);
        setCurrentTerm(termsData.current_term);

        // Auto-select current term if available
        if (termsData.current_term && selectedTerm === 'all') {
          setSelectedTerm(termsData.current_term._id);
        }
      } catch (error) {
        console.error('Error loading terms:', error);
      } finally {
        setLoadingTerms(false);
      }
    };

    loadTerms();
  }, [schoolId]);

  // Fetch grade data from API
  useEffect(() => {
    const fetchGradeData = async () => {
      if (!schoolId || !classId || !subjectId) {
        showError("Error", "Missing required information");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Build filters for this specific class and subject
        const filters: any = {
          class_id: class_id,
          subject_id: subjectId
        };

        // Enhanced term filtering
        if (selectedTerm !== 'all') {
          filters.term_id = selectedTerm;
        }

        // Sequence filtering
        if (selectedSequence !== 'all') {
          filters.sequence_number = parseInt(selectedSequence);
        }

        // Exam type filtering
        if (selectedExamType !== 'all') {
          filters.exam_type_id = selectedExamType;
        }

        // Fetch records and stats in parallel
        const [recordsResponse, statsResponse] = await Promise.all([
          getGradeRecords(schoolId as string, filters),
          getGradeStats(schoolId as string, filters)
        ]);

        setGradeRecords(recordsResponse.grade_records);
        setStats(statsResponse.stats);
      } catch (error) {
        console.error("Error fetching grade data:", error);
        showError("Error", "Failed to load grade data");
      } finally {
        setLoading(false);
      }
    };

    fetchGradeData();
  }, [schoolId, classId, subjectId, selectedTerm, selectedSequence, selectedExamType]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId || !classId || !subjectId) return;

      try {
        const [] = await Promise.all([
        ]);

        const [classResponse, subjectResponse, studentsResponse, examTypesResponse] = await Promise.all([
          getClassById(classId),
          getSubjectById(subjectId),
          getStudentsByClassAndSchool(class_id, schoolId),
          getExamTypes()
        ]);

        setClassData(classResponse);
        setSubjectData(subjectResponse);
        setStudents(studentsResponse);
        setExamTypes(examTypesResponse);
      } catch (error) {
        console.error("Error fetching additional data:", error);
      }
    };

    fetchAdditionalData();
  }, [schoolId, classId, subjectId]);

  // CRUD Functions
  const handleCreateGrade = () => {
    setGradeToEdit(null);
    setIsGradeModalOpen(true);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setGradeToEdit(grade);
    setIsGradeModalOpen(true);
  };

  const handleDeleteGrade = (grade: GradeRecord) => {
    setGradeToDelete(grade);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: GradeRecord[]) => {
    setSelectedGrades(selectedRows);
  };

  const handleGradeSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      if (gradeToEdit) {
        // Update existing grade - include school_id as required by backend
        await updateGrade(gradeToEdit._id, {
          ...data,
          school_id: schoolId
        });
      } else {
        // Create new grade - ensure class and subject are set
        await createGrade({ 
          ...data, 
          school_id: schoolId,
          class_id: classId,
          subject_id: subjectId
        });
      }

      // Refresh grades list
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      showSuccess("Success", gradeToEdit ? "Grade updated successfully" : "Grade created successfully");
    } catch (error: any) {
      showError("Error", error.message || "Failed to save grade");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackClick = () => {
    router.push(`/school-admin/grades/class?classId=${classId}`);
  };

  // Export functions
  const handleExportPDF = async () => {
    try {
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const blob = await exportGradesPDF(schoolId!, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to PDF successfully");
    } catch (error) {
      console.error('Error exporting PDF:', error);
      showError("Error", "Failed to export grades to PDF");
    }
  };

  const handleExportExcel = async () => {
    try {
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const blob = await exportGradesExcel(schoolId!, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to Excel successfully");
    } catch (error) {
      console.error('Error exporting Excel:', error);
      showError("Error", "Failed to export grades to Excel");
    }
  };

  const getGradeColor = (grade:string) => {
    const splitGrade = grade.split("/")[0];
    switch (splitGrade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'D+':
      case 'D':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'E':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // Table columns with enhanced term and sequence information
  const columns = [
    { header: "Student", accessor: (row: GradeRecord) => row.student_name },
    {
      header: "Term",
      accessor: (row: GradeRecord) => (
        <div className="text-sm">
          <div className="font-medium">{row.term}</div>
          {row.sequence_name && (
            <div className="text-gray-500 dark:text-gray-400 text-xs">
              {row.sequence_name}
            </div>
          )}
        </div>
      )
    },
    {
      header: "Exam Type",
      accessor: (row: GradeRecord) => row.exam_type || (
        <span className="text-gray-400 italic text-sm">No exam type</span>
      )
    },
    { header: "Score (/20)", accessor: (row: GradeRecord) => `${row.score}/20` },
    { header: "Grade", accessor: (row: GradeRecord) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(row.grade)}`}>
          {row.grade}
        </span>
      )
    },
    { header: "Comments", accessor: (row: GradeRecord) => row.comments || "-" },
    {
      header: "Date",
      accessor: (row: GradeRecord) => new Date(row.date_entered).toLocaleDateString()
    }
  ];

  const actions = [
    {
      label: "Edit",
      onClick: handleEditGrade
    },
    {
      label: "Delete",
      onClick: handleDeleteGrade
    }
  ];

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["school_admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={logout}
        >
          <SchoolAdminGradesSkeleton />
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin"]}>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            <Link 
              href="/school-admin/grades"
              className="hover:text-teal transition-colors"
            >
              Grades
            </Link>
            <span>/</span>
            <Link 
              href={`/school-admin/grades/class?classId=${classId}`}
              className="hover:text-teal transition-colors"
            >
              {classData?.name}
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">
              {subjectData?.name}
            </span>
          </div>

          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBackClick}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </motion.button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {classData?.name} - {subjectData?.name} Grades
                </h1>
                <p className="text-foreground/60">
                  Manage grades for this subject and class.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Export Buttons */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleExportPDF}
                className="bg-red-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-red-600 transition-colors"
                disabled={gradeRecords.length === 0}
              >
                <Download className="w-4 h-4" />
                PDF
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleExportExcel}
                className="bg-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-green-600 transition-colors"
                disabled={gradeRecords.length === 0}
              >
                <Download className="w-4 h-4" />
                Excel
              </motion.button>

              {/* Add Grade Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateGrade}
                className="bg-teal text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-teal-dark transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Grade
              </motion.button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Grades</p>
                  <p className="text-xl font-bold text-foreground">{stats.totalGrades}</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Average Score</p>
                  <p className="text-xl font-bold text-foreground">{stats.averageScore.toFixed(1)}/20</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Highest Score</p>
                  <p className="text-xl font-bold text-foreground">{stats.highestScore}/20</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Pass Rate</p>
                  <p className="text-xl font-bold text-foreground">{stats.passRate.toFixed(1)}%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Filters with Terms and Sequences */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Term Filter */}
            <div className="flex-1">
              <select
                value={selectedTerm}
                onChange={(e) => {
                  setSelectedTerm(e.target.value);
                  // Reset sequence when term changes
                  setSelectedSequence('all');
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                disabled={loadingTerms}
              >
                <option value="all">All Terms</option>
                {availableTerms.map((term) => (
                  <option key={term._id} value={term._id}>
                    {term.name} ({term.academic_year})
                    {term.is_current && " - Current"}
                  </option>
                ))}
              </select>
            </div>

            {/* Sequence Filter */}
            <div className="flex-1">
              <select
                value={selectedSequence}
                onChange={(e) => setSelectedSequence(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                disabled={selectedTerm === 'all'}
              >
                <option value="all">All Sequences</option>
                {selectedTerm !== 'all' &&
                  availableTerms
                    .find(term => term._id === selectedTerm)
                    ?.sequences.map((sequence) => (
                      <option key={sequence.sequence_number} value={sequence.sequence_number}>
                        {sequence.sequence_name}
                      </option>
                    ))
                }
              </select>
            </div>

            {/* Exam Type Filter */}
            <div className="flex-1">
              <select
                value={selectedExamType}
                onChange={(e) => setSelectedExamType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              >
                <option value="all">All Exam Types</option>
                {examTypes.map((type) => (
                  <option key={type._id} value={type._id}>
                    {type.type}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Grades Table */}
          <div className="bg-widget rounded-lg border border-stroke">
            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<GradeRecord>
                data={gradeRecords}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Grade Modal */}
        <GradeModal
          isOpen={isGradeModalOpen}
          onClose={() => {
            setIsGradeModalOpen(false);
            setGradeToEdit(null);
          }}
          onSubmit={handleGradeSubmit}
          grade={gradeToEdit}
          students={students}
          subjects={[subjectData].filter(Boolean)}
          examTypes={examTypes}
          loading={isSubmitting}
        />

        {/* Toast Container */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
