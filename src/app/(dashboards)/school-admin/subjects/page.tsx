"use client";

import { Presentation, NotebookPen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import useAuth from "@/app/hooks/useAuth";
import { SubjectCreateSchema, SubjectSchema } from "@/app/models/Subject";
import { getStudentById } from "@/app/services/StudentServices";
import { createSubject, deleteSubject, getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import DataTableFix from '@/components/utils/TableFix';
import { SchoolSchema } from "@/app/models/SchoolModel";
import { ClassSchema } from "@/app/models/ClassModel";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { getEntityNames } from "@/components/utils/getEntityNames";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import CreateSubjectModal from "./component/CreateSubjectModal";
import { verifyPassword } from "@/app/services/UserServices";
import DeleteSubjectModal from "./component/DeleteSubjectModal";
import { getClassLevelsBySchoolId } from "@/app/services/ClassLevels";
import { ClassLevelSchema } from "@/app/models/ClassLevel";

const BASE_URL = "/school-admin";

function Subject() {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [subjects, setSubjects] = useState<SubjectSchema[]>([]);
  const [class_level, setClassLevel] = useState<ClassLevelSchema[]>([])
  const [school, setSchool] = useState<SchoolSchema>({} as SchoolSchema);
  const [classes, setClasses] = useState<ClassSchema[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [subjectToDelete, setSubjectToDelete] = useState<SubjectSchema | null>(null);
  const [loadingData, setLoadingData] = useState(false);
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const { user } = useAuth();
  const schoolId = user?.school_ids?.[0] ?? null;
  const [selectedClassLevel, setSelectedClassLevel] = useState<string>("all");

  const fetchAllData = async () => {
    setLoadingData(true);

    try {
      const [subjectsResult, schoolResult, classesResult, classlevelResults] = await Promise.all([
        getSubjectsBySchoolId(schoolId as string),
        getSchoolBy_id(schoolId as string),
        getClassesBySchool(schoolId as string),
        getClassLevelsBySchoolId(schoolId as string)
      ]);

      setSubjects(subjectsResult);
      setSchool(schoolResult);
      setClasses(classesResult);
      setClassLevel(classlevelResults)
    } catch (error) {
      console.error("Failed to fetch resources:", error);
    } finally {
      setLoadingData(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [schoolId]);

  const filteredClasses = selectedClassLevel === "all"
    ? classes
    : classes.filter((cls) => cls.class_level === selectedClassLevel);

  // Filter logic
  const filteredSubjects = selectedClass === "all"
    ? subjects.filter(subject =>
      filteredClasses.some(cls =>
        Array.isArray(subject.class_id)
          ? subject.class_id.includes(cls._id)
          : subject.class_id === cls._id
      )
    )
    : subjects.filter(subject =>
      Array.isArray(subject.class_id)
        ? subject.class_id.includes(selectedClass)
        : subject.class_id === selectedClass
    );
  //Reset Class on Level Change
  useEffect(() => {
    setSelectedClass("all");
  }, [selectedClassLevel]);

  const columns = [
    { header: "Subject Code", accessor: (row: SubjectSchema) => row.subject_code },
    { header: "Name", accessor: (row: SubjectSchema) => row.name || "N/A" },
    {
      header: "Class",
      accessor: (row: SubjectSchema) =>
        getEntityNames(row.class_id, classes, "ClassName")
    },
    { header: "Coefficient", accessor: (row: SubjectSchema) => row.coefficient || "N/A" },
    {
      header: "Compulsory",
      accessor: (row: SubjectSchema) => (row.compulsory ? "Yes" : "No"),
    },
  ];

  const actions = [
    {
      label: "View",
      onClick: (sub: SubjectSchema) => {
        router.push(`${BASE_URL}/subjects/view?id=${sub._id}`);
      },
    },
    {
      label: "Delete",
      onClick: (sub: SubjectSchema) => {
        setSubjectToDelete(sub);
      },
    },
  ];

  const handleDeleteSubject = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    //setLoadingData(true);
    if (!subjectToDelete || !user) {
      return;
    }
    const passwordVerified = await verifyPassword(password, user.email);
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);

      // ✅ Fix: Reset loading/submitting states even when password fails
      setIsSubmitting(false);
      //setLoadingData(false);
      setSubmitStatus("failure");
      setTimeout(() => {
        setSubjectToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
      return;
    }
    try {
      await deleteSubject(subjectToDelete.subject_id)
      fetchAllData();
      setSubmitStatus("success");
      setNotificationMessage("Subject Deleted successfully!");
      setNotificationType("success");
      setIsNotificationCard(true);

      setTimeout(() => {
        setSubjectToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
    } catch (error) {
      console.error("Error Deleting Invitation:", error);

      setSubmitStatus("failure");
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred while deleting the Subject.";

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);
      setLoadingData(false);
    }
  }
  const handleCreateSubject = async (subjectData: SubjectCreateSchema) => {
    setIsSubmitting(true);         // Start submitting
    setSubmitStatus(null);
    //setLoadingData(true);
    try {
      const newSubject: SubjectCreateSchema = {
        subject_code: subjectData.subject_code,
        compulsory: subjectData.compulsory,
        school_id: schoolId as string,
        class_id: subjectData.class_id,
        name: subjectData.name,
        description: subjectData.description,
        coefficient: subjectData.coefficient,
      }
      const data = await createSubject(newSubject)
      if (data) {
        fetchAllData();
        setSubmitStatus("success");                 // ✅ update success
        setNotificationMessage("Subject created successfully!");
        setNotificationType("success");
        setIsNotificationCard(true);

        setTimeout(() => {
          setIsModalOpen(false);
          setSubmitStatus(null); // reset
        }, 10000);
      }
    } catch (error) {
      console.error("Error creating Subject:", error);
      setSubmitStatus("failure");                  // ✅ update failure
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred while creating the subject.";

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);                     // ✅ end submitting
      //setLoadingData(false);
    }
  }
  return (
    <div>
      {isNotificationCard && (
        <NotificationCard
          title="Notification"
          icon={
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
              <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
            </svg>
          }
          message={notificationMessage}
          onClose={() => setIsNotificationCard(false)}
          type={notificationType}
          isVisible={isNotificationCard}
          isFixed={true}
        />
      )}
      <div className="flex justify-between items-center mb-4">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: 'spring', stiffness: 300 }}
          onClick={() => setIsModalOpen(true)}
          className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
        >
          Add Subject
        </motion.button>

        <div className="flex gap-5">
          <select
            value={selectedClassLevel}
            onChange={(e) => setSelectedClassLevel(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal"
          >
            <option value="all">All Levels</option>
            {class_level.map((level) => (
              <option key={level._id} value={level._id}>
                {level.name}
              </option>
            ))}
          </select>

          {/* Class Filter Dropdown */}
          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal "
          >
            <option value="all">All Classes</option>
            {filteredClasses.map((cls) => (
              <option key={cls._id} value={cls._id}>
                {cls.name}
              </option>
            ))}
          </select>
        </div>

      </div>

      <DataTableFix
        columns={columns}
        data={filteredSubjects}
        actions={actions}
        defaultItemsPerPage={5}
        loading={loadingData}
        onLoadingChange={setLoadingData}
        showCheckbox={true}
      />

      {isModalOpen && (
        <CreateSubjectModal
          onClose={() => { setIsModalOpen(false); setSubmitStatus(null); }}
          onSave={handleCreateSubject}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
          SchoolId={schoolId as string}
        />
      )}
      {subjectToDelete && (
        <DeleteSubjectModal
          subjectName={subjectToDelete.name || ""}
          onClose={() => { setSubjectToDelete(null); setSubmitStatus(null); }}
          onDelete={handleDeleteSubject}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
}

export default function Page() {
  const navigation = {
    icon: NotebookPen,
    baseHref: `${BASE_URL}/subjects`,
    title: "Subjects"
  };
  const { logout } = useAuth();

  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <Subject />
      </SchoolLayout>
    </Suspense>
  );
}
