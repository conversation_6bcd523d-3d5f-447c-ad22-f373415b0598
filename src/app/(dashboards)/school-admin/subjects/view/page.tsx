"use client";

import { NotebookPen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import ClassDetailView from "@/components/Dashboard/ReusableComponents/ClassViewComponent";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { getClasses, getClassesBySchool } from "@/app/services/ClassServices";
import { ClassSchema } from "@/app/models/ClassModel";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import PageWrapper from "@/components/Dashboard/ReusableComponents/ClassComponent";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { SubjectSchema, SubjectUpdateSchema } from "@/app/models/Subject";
import { deleteSubject, getSubjectBy_Id, getSubjectById, updateSubject } from "@/app/services/SubjectServices";
import { verifyPassword } from "@/app/services/UserServices";
import CreateSubjectModal from "../component/CreateSubjectModal";
import { getClassLevelsBySchoolId } from "@/app/services/ClassLevels";
import DeleteSubjectModal from "../component/DeleteSubjectModal";
import Loading from "@/components/widgets/Loading";
const BASE_URL = "/school-admin";

const navigation = {
    icon: NotebookPen,
    baseHref: `${BASE_URL}/subjects`,
    title: "Subjects"
};
export function ViewSubject() {
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
    const [school, setSchool] = useState<SchoolSchema | null>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [subject, setSubject] = useState<SubjectSchema | null>(null);
    const [loadingData, setLoading] = useState(false);
    const [allClasses, setClasses] = useState<ClassSchema[]>([]);
    const { user } = useAuth();
    const schoolId = user?.school_ids?.[0] ?? null;
    const searchParams = useSearchParams();
    const subId = searchParams.get("id");
    const router = useRouter();

    const fetchSchool = async (schoolId: string) => {
        if (!schoolId) {
            return;
        }
        setLoading(true);
        try {
            const data = await getSchoolBy_id(schoolId);
            setSchool(data);
        } catch (error) {
            console.error("Error fetching school:", error);
            return null;
        } finally {
            setLoading(false);
        }
    }
    const fetchClasses = async (schoolId: string) => {
        if (!schoolId) {
            return;
        }
        setLoading(true);
        try {
            const data = await getClassesBySchool(schoolId as string);
            setClasses(data);
        } catch (error) {
            console.error("Error fetching classes:", error);
            return [];
        } finally {
            setLoading(false);
        }
    }
    const fetchSubject = async (subId: string) => {
        if (!subId) {
            return;
        }
        setLoading(true);
        try {
            const data = await getSubjectBy_Id(subId)
            setSubject(data);
        } catch (error) {

        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchSubject(subId as string);
        fetchSchool(schoolId as string);
        fetchClasses(schoolId as string);
        // Fetch classes only if schoolId is available
    }, [])

    //console.log(subject)
    const handleUpdate = async (subjectData: SubjectSchema) => {
        setIsSubmitting(true);
        setSubmitStatus(null);
        try {
            const updateSubjectData: SubjectUpdateSchema = {
                _id: subjectData._id,
                subject_code: subjectData.subject_code,
                compulsory: subjectData.compulsory,
                class_id: subjectData.class_id,
                name: subjectData.name,
                description: subjectData.description,
                coefficient: subjectData.coefficient,
                department: subjectData.department,
            }
            const data = await updateSubject(subjectData.subject_id, updateSubjectData)
            if (data) {
                setSubmitStatus("success");
                setIsNotificationCard(true);
                setNotificationMessage("Subject updated successfully.");
                fetchSubject(subId as string);
                fetchSchool(schoolId as string);
                fetchClasses(schoolId as string);
            }

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Error updating Subject:";
            setSubmitStatus("failure");                  // ✅ update failure
            setNotificationMessage(errorMessage);
            setIsNotificationCard(true);
            setNotificationType("error");
        } finally {
            setIsSubmitting(false);                     // ✅ end submitting
        }
    }
    const handleDeleteSubject = async (password: string) => {
        setIsSubmitting(true);
        setSubmitStatus(null);

        try {
            const passwordVerified = user ? await verifyPassword(password, user.email) : false;

            if (!passwordVerified) {
                setNotificationMessage("Invalid Password!");
                setNotificationType("error");
                setIsNotificationCard(true);
                setSubmitStatus("failure");

                setTimeout(() => {
                    setIsDeleteModalOpen(false);
                    setSubmitStatus(null);
                    router.push(`${BASE_URL}/subjects`);
                }, 5000);

                return;
            }

            if (!subId) {
                throw new Error("Subject ID is null. Cannot delete subject.");
            }

            await deleteSubject(subject?.subject_id as string);

            setSubmitStatus("success");
            setNotificationMessage("Subject deleted successfully!");
            setNotificationType("success");
            setIsNotificationCard(true);

            setTimeout(() => {
                setIsDeleteModalOpen(false);
                setSubmitStatus(null);
                router.push(`${BASE_URL}/subjects`);
            }, 5000);

        } catch (error) {
            console.error("Delete subject error:", error);
            setSubmitStatus("failure");
            setNotificationMessage("Failed to delete subject. Please try again.");
            setNotificationType("error");
            setIsNotificationCard(true);
        } finally {
            setIsSubmitting(false);
        } 
        
        const handleDeleteSubject = async (password: string) => {
            setIsSubmitting(true);
            setSubmitStatus(null);

            try {
                const passwordVerified = user ? await verifyPassword(password, user.email) : false;

                if (!passwordVerified) {
                    setNotificationMessage("Invalid Password!");
                    setNotificationType("error");
                    setIsNotificationCard(true);
                    setSubmitStatus("failure");

                    setTimeout(() => {
                        setIsDeleteModalOpen(false);
                        setSubmitStatus(null);
                        router.push(`${BASE_URL}/subjects`);
                    }, 5000);

                    return;
                }

                if (!subId) {
                    throw new Error("Subject ID is null. Cannot delete subject.");
                }

                await deleteSubject(subject?.subject_id as string);

                setSubmitStatus("success");
                setNotificationMessage("Subject deleted successfully!");
                setNotificationType("success");
                setIsNotificationCard(true);

                setTimeout(() => {
                    setIsDeleteModalOpen(false);
                    setSubmitStatus(null);
                    router.push(`${BASE_URL}/subjects`);
                }, 5000);

            } catch (error) {
                console.error("Delete subject error:", error);
                setSubmitStatus("failure");
                setNotificationMessage("Failed to delete subject. Please try again.");
                setNotificationType("error");
                setIsNotificationCard(true);
            } finally {
                setIsSubmitting(false);
            }
        };
    };
  if (loadingData) return <Loading />;

    return (
        <div>
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                            <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
                        </svg>
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                    autoClose={true}
                    duration={4000}
                />
            )}
            {isUpdateModalOpen && (
                <CreateSubjectModal
                    onClose={() => {
                        setIsUpdateModalOpen(false);
                        setSubmitStatus(null);
                    }} initialData={subject}
                    onSave={handleUpdate}
                    isSubmitting={isSubmitting}
                    submitStatus={submitStatus}
                    SchoolId={schoolId as string}
                />
            )}
            {isDeleteModalOpen && subject && (
                <DeleteSubjectModal
                    subjectName={subject.name || ""}
                    onClose={() => { setIsDeleteModalOpen(false); setSubmitStatus(null); }}
                    onDelete={handleDeleteSubject}
                    submitStatus={submitStatus}
                    isSubmitting={isSubmitting}
                />
            )}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h1 className="text-2xl font-bold text-foreground mb-4">
                    {subject?.name} ({subject?.subject_code}) - <span className="opacity-60">{school?.name}</span>
                </h1>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {/* Subject Code */}
                    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Subject Code</p>
                        <p className="text-sm text-foreground">{subject?.subject_code || "N/A"}</p>
                    </div>

                    {/* Coefficient */}
                    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Coefficient</p>
                        <p className="text-sm text-foreground">{subject?.coefficient ?? "N/A"}</p>
                    </div>

                    {/* Department */}
                    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Department</p>
                        <p className="text-sm text-foreground">{subject?.department || "None"}</p>
                    </div>

                    {/* Compulsory */}
                    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Compulsory</p>
                        <p className="text-sm text-foreground">{subject?.compulsory ? "Yes" : "No"}</p>
                    </div>

                    {/* Description */}
                    <div className="sm:col-span-2 bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">Description</p>
                        <p className="text-sm text-foreground whitespace-pre-line">{subject?.description}</p>
                    </div>

                    {/* Linked Classes */}
                    <div className="sm:col-span-2 bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-2">Linked Classes</p>
                        {Array.isArray(subject?.class_id) && subject.class_id.length > 0 ? (
                            <ul className="list-disc pl-5 space-y-1">
                                {subject.class_id.map((classId: string) => {
                                    const classItem = allClasses.find((c) => c._id === classId);
                                    return (
                                        <li key={classId} className="text-sm text-foreground">
                                            {classItem ? classItem.name : "Unknown Class"}
                                        </li>
                                    );
                                })}
                            </ul>
                        ) : (
                            <p className="text-sm text-foreground italic">No classes linked</p>
                        )}
                    </div>
                </div>

                {/* Buttons */}
                <div className="flex justify-end space-x-2">
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                        onClick={() => setIsUpdateModalOpen(true)}
                        className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-700"
                    >
                        Edit Subject
                    </motion.button>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                        onClick={() => setIsDeleteModalOpen(true)}
                        className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                        Delete Subject
                    </motion.button>
                </div>
            </div>

        </div>
    )
}

export default function Page() {
    const { logout } = useAuth();
    const { user } = useAuth();
    return (
        <Suspense fallback={
            <div>
                <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
                    <CircularLoader size={32} color="teal" />
                </div>
            </div>
        }>
            <SchoolLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => logout()}
            >
                <ViewSubject />
            </SchoolLayout>
        </Suspense>
    );
}

