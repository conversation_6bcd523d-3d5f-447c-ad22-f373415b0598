"use client";

import React, { useState, useEffect } from "react";
import { X, ChevronLeft, ChevronRight, Search, UserCheck, UserPlus } from "lucide-react";
import { 
  StaffSchema, 
  StaffCreateSchema, 
  TeacherSearchResult,
  createStaff,
  updateStaff,
  searchTeachers
} from "@/app/services/StaffServices";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";

interface CreateStaffModalProps {
  onClose: () => void;
  onSave: (staff: StaffSchema, isEdit: boolean) => void;
  initialData?: StaffSchema | null;
  schoolId: string;
}

type StepType = "role_selection" | "teacher_search" | "personal_info" | "permissions" | "assignments";

export default function CreateStaffModal({
  onClose,
  onSave,
  initialData,
  schoolId,
}: CreateStaffModalProps) {
  const [currentStep, setCurrentStep] = useState<StepType>("role_selection");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<TeacherSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<TeacherSearchResult | null>(null);

  const [formData, setFormData] = useState<StaffCreateSchema>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    role_template: "teacher",
    school_id: schoolId,
    permissions: undefined,
    assigned_classes: [],
    is_existing_teacher: false,
    teacher_id: undefined,
  });

  // Populate form with initial data if editing
  useEffect(() => {
    if (initialData) {
      console.log("Initial Data:", initialData);
      console.log("Permissions structure:", initialData.permissions);
      setFormData({
        first_name: initialData.first_name,
        last_name: initialData.last_name,
        email: initialData.email,
        phone: initialData.phone || "",
        role_template: initialData.permissions?.role_template || "custom",
        school_id: schoolId,
        permissions: initialData.permissions?.permissions,
        assigned_classes: initialData.permissions?.assigned_classes || [],
        is_existing_teacher: false,
      });
      setCurrentStep("personal_info");
    }
  }, [initialData, schoolId]);

  // Initialize permissions when role changes
  useEffect(() => {
    if (formData.role_template && !initialData && !formData.permissions) {
      const defaultPermissions = getDefaultPermissionsForRole(formData.role_template);
      setFormData(prev => ({
        ...prev,
        permissions: defaultPermissions
      }));
    }
  }, [formData.role_template, initialData, formData.permissions]);

  // Handle teacher search
  const handleTeacherSearch = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchTeachers(query);
      setSearchResults(results);
    } catch (error) {
      console.error("Error searching teachers:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle teacher selection
  const handleTeacherSelect = (teacher: TeacherSearchResult) => {
    setSelectedTeacher(teacher);
    setFormData(prev => ({
      ...prev,
      first_name: teacher.first_name,
      last_name: teacher.last_name,
      email: teacher.email,
      phone: teacher.phone || "",
      is_existing_teacher: true,
      teacher_id: teacher._id,
    }));
    setCurrentStep("permissions");
  };

  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      if (initialData) {
        // Update existing staff
        const result = await updateStaff(initialData.staff_id || initialData._id, {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone: formData.phone,
          role_template: formData.role_template,
          permissions: formData.permissions,
          assigned_classes: formData.assigned_classes,
          school_id: schoolId,
        });
        onSave(result.user, true);
      } else {
        // Create new staff
        const result = await createStaff(formData);
        onSave(result.user, false);
      }
      onClose();
    } catch (error) {
      console.error("Error saving staff:", error);
      alert("Failed to save staff member. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof StaffCreateSchema, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Get permissions to display based on role
  const getPermissionsToDisplay = (role: string) => {
    const defaultPermissions = getDefaultPermissionsForRole(role);

    // For school_admin, show all available permissions
    if (role === 'school_admin') {
      return getAllAvailablePermissions();
    }

    // For teacher, show only their default permissions (non-modifiable)
    if (role === 'teacher') {
      return defaultPermissions;
    }

    // For other roles, show their default permissions (modifiable)
    return defaultPermissions;
  };

  // Get all available permissions (for school admin)
  const getAllAvailablePermissions = () => {
    return getDefaultPermissionsForRole('school_admin');
  };

  // Generate permission categories based on role
  const getPermissionCategories = (role: string) => {
    const permissionsToShow = getPermissionsToDisplay(role);
    const categories = [];

    // Students Management
    if (permissionsToShow.students) {
      categories.push({
        category: 'students',
        title: 'Students Management',
        permissions: [
          { key: 'view_all_students', label: 'View all students' },
          { key: 'add_edit_delete_students', label: 'Add, edit & delete students' },
          { key: 'generate_id_cards', label: 'Generate ID cards' },
          { key: 'generate_report_cards', label: 'Generate report cards' }
        ].filter(perm => permissionsToShow.students.hasOwnProperty(perm.key))
      });
    }

    // classes
    if (permissionsToShow.classes) {
      categories.push({
        category: 'classes',
        title: 'Classes Management',
        permissions: [
          { key: 'view_all_classes', label: 'View all classes' },
          { key: 'add_edit_delete_classes', label: 'Add, edit & delete classes' },
          { key: 'manage_class_schedules', label: 'Manage class schedules' },
          { key: 'assign_teachers_to_classes', label: 'Assign teachers to classes' }
        ].filter(perm => permissionsToShow.classes.hasOwnProperty(perm.key))
      });
    }

    // Academic Records
    if (permissionsToShow.academic_records) {
      categories.push({
        category: 'academic_records',
        title: 'Academic Records',
        permissions: [
          { key: 'view_grades_assigned_classes', label: 'View grades (assigned classes)' },
          { key: 'enter_edit_grades_assigned_classes', label: 'Enter/edit grades (assigned classes)' },
          { key: 'view_all_school_grades', label: 'View all school grades' },
          { key: 'take_attendance_assigned_classes', label: 'Take attendance (assigned classes)' },
          { key: 'view_all_attendance', label: 'View all attendance records' },
          { key: 'manage_terms', label: 'Manage terms' },
          { key: 'manage_timetables', label: 'Manage timetables' },
          { key: 'manage_periods', label: 'Manage periods' },
          { key: 'manage_subjects', label: 'Manage subjects' },
          { key: 'manage_classes', label: 'Manage classes' },
          { key: 'manage_exam_types', label: 'Manage exam types' },
          { key: 'manage_discipline', label: 'Manage discipline' }
        ].filter(perm => permissionsToShow.academic_records.hasOwnProperty(perm.key))
      });
    }

    // Staff Management
    if (permissionsToShow.staff) {
      categories.push({
        category: 'staff',
        title: 'Staff Management',
        permissions: [
          { key: 'view_staff_list', label: 'View staff list' },
          { key: 'add_edit_delete_staff', label: 'Add, edit & delete staff' },
          { key: 'manage_staff_permissions', label: 'Manage staff permissions' },
          { key: 'reset_staff_passwords', label: 'Reset staff passwords' },
          { key: 'manage_teacher_assignments', label: 'Manage teacher assignments' }
        ].filter(perm => permissionsToShow.staff.hasOwnProperty(perm.key))
      });
    }

    // Announcements
    if (permissionsToShow.announcements) {
      categories.push({
        category: 'announcements',
        title: 'Announcements',
        permissions: [
          { key: 'view_announcements', label: 'View announcements' },
          { key: 'create_edit_announcements', label: 'Create & edit announcements' },
          { key: 'delete_announcements', label: 'Delete announcements' },
          { key: 'publish_announcements', label: 'Publish announcements' }
        ].filter(perm => permissionsToShow.announcements.hasOwnProperty(perm.key))
      });
    }

    // Resources
    if (permissionsToShow.resources) {
      categories.push({
        category: 'resources',
        title: 'Resources',
        permissions: [
          { key: 'view_resources', label: 'View resources' },
          { key: 'add_edit_delete_resources', label: 'Add, edit & delete resources' },
          { key: 'manage_resource_categories', label: 'Manage resource categories' }
        ].filter(perm => permissionsToShow.resources.hasOwnProperty(perm.key))
      });
    }

    // Reports
    if (permissionsToShow.reports) {
      categories.push({
        category: 'reports',
        title: 'Reports',
        permissions: [
          { key: 'generate_student_reports', label: 'Generate student reports' },
          { key: 'generate_financial_reports', label: 'Generate financial reports' },
          { key: 'generate_attendance_reports', label: 'Generate attendance reports' },
          { key: 'export_data', label: 'Export data' }
        ].filter(perm => permissionsToShow.reports.hasOwnProperty(perm.key))
      });
    }

    // Communications
    if (permissionsToShow.communications) {
      categories.push({
        category: 'communications',
        title: 'Communications',
        permissions: [
          { key: 'view_announcements', label: 'View announcements' },
          { key: 'create_edit_announcements', label: 'Create & edit announcements' },
          { key: 'delete_announcements', label: 'Delete announcements' },
          { key: 'publish_announcements', label: 'Publish announcements' },
          { key: 'manage_resources', label: 'Manage resources' }
        ].filter(perm => permissionsToShow.communications.hasOwnProperty(perm.key))
      });
    }

    // School Management
    if (permissionsToShow.school_management) {
      categories.push({
        category: 'school_management',
        title: 'School Management',
        permissions: [
          { key: 'view_school_info', label: 'View school info' },
          { key: 'edit_school_info', label: 'Edit school info' },
          { key: 'manage_school_settings', label: 'Manage school settings' }
        ].filter(perm => permissionsToShow.school_management.hasOwnProperty(perm.key))
      });
    }

    // Financials
    if (permissionsToShow.financials) {
      categories.push({
        category: 'financials',
        title: 'Financials',
        permissions: [
          { key: 'view_student_fee_balances', label: 'View student fee balances' },
          { key: 'record_fee_payments', label: 'Record fee payments' },
          { key: 'manage_school_credit_balance', label: 'Manage school credit balance' },
          { key: 'view_financial_reports', label: 'View financial reports' },
          { key: 'manage_fee_types', label: 'Manage fee types' },
          { key: 'view_transactions', label: 'View transactions' }
        ].filter(perm => permissionsToShow.financials.hasOwnProperty(perm.key))
      });
    }

    return categories;
  };

  // Get default permissions for a role
  const getDefaultPermissionsForRole = (role: string) => {
    const defaultPermissions = {
      school_admin: {
        students: {
          view_all_students: true,
          add_edit_delete_students: true,
          generate_id_cards: true,
          generate_report_cards: true
        },
        academic_records: {
          view_grades_assigned_classes: true,
          enter_edit_grades_assigned_classes: true,
          view_all_school_grades: true,
          take_attendance_assigned_classes: true,
          view_all_attendance: true,
          manage_terms: true,
          manage_timetables: true,
          manage_periods: true,
          manage_subjects: true,
          manage_classes: true,
          manage_exam_types: true,
          manage_discipline: true
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: true,
          manage_staff_permissions: true,
          reset_staff_passwords: true,
          manage_teacher_assignments: true
        },
        financials: {
          view_student_fee_balances: true,
          record_fee_payments: true,
          manage_school_credit_balance: true,
          view_financial_reports: true,
          manage_fee_types: true,
          view_transactions: true
        },
        classes: {
          view_all_classes: true,
          add_edit_delete_classes: true,
          manage_class_schedules: true,
          assign_teachers_to_classes: true
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: true,
          delete_announcements: true,
          publish_announcements: true
        },
        resources: {
          view_resources: true,
          add_edit_delete_resources: true,
          manage_resource_categories: true
        },
        reports: {
          generate_student_reports: true,
          generate_financial_reports: true,
          generate_attendance_reports: true,
          export_data: true
        },
        communications: {
          view_announcements: true,
          create_edit_announcements: true,
          delete_announcements: true,
          publish_announcements: true,
          manage_resources: true
        },
        school_management: {
          view_school_info: true,
          edit_school_info: true,
          manage_school_settings: true
        }
      },
      teacher: {
        students: {
          view_all_students: true,
          add_edit_delete_students: false,
          generate_id_cards: false,
          generate_report_cards: true
        },
        academic_records: {
          view_grades_assigned_classes: true,
          enter_edit_grades_assigned_classes: true,
          view_all_school_grades: false,
          take_attendance_assigned_classes: true,
          view_all_attendance: false,
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: false,
          manage_staff_permissions: false,
          reset_staff_passwords: false
        },
        classes: {
          view_all_classes: true,
          add_edit_delete_classes: false,
          manage_class_schedules: true,
          assign_teachers_to_classes: false,
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: false,
          delete_announcements: false,
          publish_announcements: false
        }
      },
      bursar: {
        students: {
          view_all_students: true,
          add_edit_delete_students: false,
          generate_id_cards: false,
          generate_report_cards: false
        },
        academic_records: {
          view_grades_assigned_classes: false,
          enter_edit_grades_assigned_classes: false,
          view_all_school_grades: false,
          take_attendance_assigned_classes: false,
          view_all_attendance: false,
          manage_terms: false,
          manage_timetables: false,
          manage_periods: false,
          manage_subjects: false,
          manage_classes: false,
          manage_exam_types: false,
          manage_discipline: false
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: false,
          manage_staff_permissions: false,
          reset_staff_passwords: false,
          manage_teacher_assignments: false
        },
        financials: {
          view_student_fee_balances: true,
          record_fee_payments: true,
          manage_school_credit_balance: true,
          view_financial_reports: true,
          manage_fee_types: true,
          view_transactions: true
        },
        classes: {
          view_all_classes: false,
          add_edit_delete_classes: false,
          manage_class_schedules: false,
          assign_teachers_to_classes: false
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: false,
          delete_announcements: false,
          publish_announcements: false
        },
        resources: {
          view_resources: true,
          add_edit_delete_resources: false,
          manage_resource_categories: false
        },
        reports: {
          generate_student_reports: false,
          generate_financial_reports: true,
          generate_attendance_reports: false,
          export_data: true
        },
        communications: {
          view_announcements: true,
          create_edit_announcements: false,
          delete_announcements: false,
          publish_announcements: false,
          manage_resources: false
        },
        school_management: {
          view_school_info: true,
          edit_school_info: false,
          manage_school_settings: false
        }
      },
      dean_of_studies: {
        students: {
          view_all_students: true,
          add_edit_delete_students: true,
          generate_id_cards: true,
          generate_report_cards: true
        },
        academic_records: {
          view_grades_assigned_classes: true,
          enter_edit_grades_assigned_classes: true,
          view_all_school_grades: true,
          take_attendance_assigned_classes: true,
          view_all_attendance: true,
          manage_terms: true,
          manage_timetables: true,
          manage_periods: true,
          manage_subjects: true,
          manage_classes: true,
          manage_exam_types: true,
          manage_discipline: true
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: false,
          manage_staff_permissions: false,
          reset_staff_passwords: false,
          manage_teacher_assignments: true
        },
        financials: {
          view_student_fee_balances: false,
          record_fee_payments: false,
          manage_school_credit_balance: false,
          view_financial_reports: false,
          manage_fee_types: false,
          view_transactions: false
        },
        classes: {
          view_all_classes: true,
          add_edit_delete_classes: true,
          manage_class_schedules: true,
          assign_teachers_to_classes: true
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: true,
          delete_announcements: false,
          publish_announcements: true
        },
        resources: {
          view_resources: true,
          add_edit_delete_resources: true,
          manage_resource_categories: true
        },
        reports: {
          generate_student_reports: true,
          generate_financial_reports: false,
          generate_attendance_reports: true,
          export_data: true
        },
        communications: {
          view_announcements: true,
          create_edit_announcements: true,
          delete_announcements: false,
          publish_announcements: true,
          manage_resources: true
        },
        school_management: {
          view_school_info: true,
          edit_school_info: false,
          manage_school_settings: false
        }
      }
    };

    return defaultPermissions[role as keyof typeof defaultPermissions] || {};
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case "role_selection":
        return "Select Staff Type";
      case "teacher_search":
        return "Search for Teacher";
      case "personal_info":
        return "Personal Information";
      case "permissions":
        return "Role & Permissions";
      case "assignments":
        return "Class Assignments";
      default:
        return "Add Staff Member";
    }
  };

  // Handle next step
  const handleNext = () => {
    switch (currentStep) {
      case "role_selection":
        if (formData.role_template === "teacher") {
          setCurrentStep("teacher_search");
        } else {
          setCurrentStep("personal_info");
        }
        break;
      case "teacher_search":
        if (selectedTeacher) {
          setCurrentStep("permissions");
        } else {
          setCurrentStep("personal_info");
        }
        break;
      case "personal_info":
        setCurrentStep("permissions");
        break;
      case "permissions":
        if (formData.role_template === "teacher" && formData.assigned_classes?.length === 0) {
          setCurrentStep("assignments");
        } else {
          handleSubmit();
        }
        break;
      case "assignments":
        handleSubmit();
        break;
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    switch (currentStep) {
      case "teacher_search":
        setCurrentStep("role_selection");
        break;
      case "personal_info":
        if (formData.role_template === "teacher") {
          setCurrentStep("teacher_search");
        } else {
          setCurrentStep("role_selection");
        }
        break;
      case "permissions":
        setCurrentStep("personal_info");
        break;
      case "assignments":
        setCurrentStep("permissions");
        break;
    }
  };

  // Check if current step is valid
  const isStepValid = () => {
    switch (currentStep) {
      case "role_selection":
        return formData.role_template !== "";
      case "teacher_search":
        return true; // Always allow to proceed, whether teacher is found or not
      case "personal_info":
        return formData.first_name && formData.last_name && formData.email;
      case "permissions":
        return true; // Always valid, permissions are optional
      case "assignments":
        return true; // Always valid, assignments are optional
      default:
        return false;
    }
  };

  // @ts-ignore
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="max-h-svh overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl mx-4 sm:mx-6 md:mx-0 p-6 relative">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-foreground">
            {initialData ? "Edit Staff Member" : getStepTitle()}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {/* Role Selection Step */}
          {currentStep === "role_selection" && !initialData && (
            <div className="space-y-6">
              <p className="text-foreground/70">
                First, select the type of staff member you want to add:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleInputChange("role_template", "teacher")}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.role_template === "teacher"
                      ? "border-teal bg-teal/10"
                      : "border-stroke hover:border-teal/50"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <UserCheck className="h-8 w-8 text-teal" />
                    <div>
                      <h3 className="font-medium text-foreground">Teacher</h3>
                      <p className="text-sm text-foreground/60">
                        Can teach multiple schools and needs access codes
                      </p>
                    </div>
                  </div>
                </motion.div>

                {["school_admin", "bursar", "dean_of_studies"].map((role) => (
                  <motion.div
                    key={role}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleInputChange("role_template", role)}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      formData.role_template === role
                        ? "border-teal bg-teal/10"
                        : "border-stroke hover:border-teal/50"
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <UserPlus className="h-8 w-8 text-teal" />
                      <div>
                        <h3 className="font-medium text-foreground">
                          {role === "school_admin" ? "School Admin" :
                           role === "dean_of_studies" ? "Dean of Studies" :
                           "Bursar"}
                        </h3>
                        <p className="text-sm text-foreground/60">
                          {role === "school_admin" ? "Full administrative access to school" :
                           role === "dean_of_studies" ? "Academic oversight and management" :
                           "Financial management and operations"}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Teacher Search Step */}
          {currentStep === "teacher_search" && (
            <div className="space-y-6">
              <div>
                <p className="text-foreground/70 mb-4">
                  Search for an existing teacher or create a new one:
                </p>
                
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/40" />
                  <input
                    type="text"
                    placeholder="Search by name, email, or phone..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      handleTeacherSearch(e.target.value);
                    }}
                    className="w-full pl-10 pr-4 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  />
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <CircularLoader size={16} color="teal" />
                    </div>
                  )}
                </div>
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-foreground">Existing Teachers:</h4>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {searchResults.map((teacher) => (
                      <motion.div
                        key={teacher._id}
                        whileHover={{ scale: 1.01 }}
                        onClick={() => handleTeacherSelect(teacher)}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          selectedTeacher?._id === teacher._id
                            ? "border-teal bg-teal/10"
                            : "border-stroke hover:border-teal/50"
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-foreground">
                              {teacher.first_name} {teacher.last_name}
                            </p>
                            <p className="text-sm text-foreground/60">{teacher.email}</p>
                            {teacher.phone && (
                              <p className="text-sm text-foreground/60">{teacher.phone}</p>
                            )}
                          </div>
                          {selectedTeacher?._id === teacher._id && (
                            <UserCheck className="h-5 w-5 text-teal" />
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {searchQuery.length >= 2 && searchResults.length === 0 && !isSearching && (
                <div className="text-center py-8">
                  <UserPlus className="h-12 w-12 text-foreground/30 mx-auto mb-2" />
                  <p className="text-foreground/60">No teachers found. Continue to create a new teacher.</p>
                </div>
              )}
            </div>
          )}

          {/* Personal Information Step */}
          {currentStep === "personal_info" && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-foreground">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.first_name}
                    onChange={(e) => handleInputChange("first_name", e.target.value)}
                    placeholder="Enter first name"
                    required
                    className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-foreground">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.last_name}
                    onChange={(e) => handleInputChange("last_name", e.target.value)}
                    placeholder="Enter last name"
                    required
                    className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-foreground">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="Enter email address"
                  required
                  className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-foreground">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="Enter phone number"
                  className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                />
              </div>
            </div>
          )}

          {/* Permissions Step */}
          {currentStep === "permissions" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-foreground mb-2">Role & Permissions</h3>
                <p className="text-foreground/60">
                  Configure the permissions for this staff member. You can customize individual permissions or use role defaults.
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-foreground/70">
                  <strong>Selected Role:</strong> {
                    formData.role_template === "school_admin" ? "School Admin" :
                    formData.role_template === "dean_of_studies" ? "Dean of Studies" :
                    formData.role_template.charAt(0).toUpperCase() + formData.role_template.slice(1)
                  }
                </p>
                {formData.role_template === 'teacher' && (
                  <p className="text-sm text-amber-600 dark:text-amber-400 mt-2">
                    <strong>Note:</strong> Teachers have a fixed dashboard with predefined permissions. These permissions cannot be modified.
                  </p>
                )}
                {formData.role_template === 'school_admin' && (
                  <p className="text-sm text-blue-600 dark:text-blue-400 mt-2">
                    <strong>Note:</strong> School Admin has access to all permissions and can customize them as needed.
                  </p>
                )}
              </div>

              {/* Permission Categories */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {getPermissionCategories(formData.role_template).map((section) => (
                  <div key={section.category} className="border border-stroke rounded-lg p-4">
                    <h4 className="font-medium text-foreground mb-3">{section.title}</h4>
                    <div className="space-y-2">
                      {section.permissions.map((permission) => (
                        <label key={permission.key} className={`flex items-center space-x-2 ${formData.role_template === 'teacher' ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'}`}>
                          <input
                            type="checkbox"
                            checked={formData.permissions?.[section.category]?.[permission.key] || false}
                            disabled={formData.role_template === 'teacher'}
                            onChange={(e) => {
                              if (formData.role_template === 'teacher') return; // Prevent changes for teachers

                              const newPermissions = {
                                ...formData.permissions,
                                [section.category]: {
                                  ...formData.permissions?.[section.category],
                                  [permission.key]: e.target.checked
                                }
                              };
                              handleInputChange("permissions", newPermissions);
                            }}
                            className={`w-4 h-4 text-teal border-gray-300 rounded focus:ring-teal focus:ring-2 ${formData.role_template === 'teacher' ? 'cursor-not-allowed' : ''}`}
                          />
                          <span className="text-sm text-foreground">{permission.label}</span>
                          {formData.role_template === 'teacher' && (
                            <span className="text-xs text-amber-600 dark:text-amber-400">(Fixed)</span>
                          )}
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    // Apply default permissions for role
                    const defaultPerms = getDefaultPermissionsForRole(formData.role_template);
                    handleInputChange("permissions", defaultPerms);
                  }}
                  className="px-3 py-2 text-sm bg-teal text-white rounded-md hover:bg-teal-600"
                >
                  Apply Role Defaults
                </button>
                <button
                  type="button"
                  onClick={() => handleInputChange("permissions", {})}
                  className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  Clear All
                </button>
              </div>
            </div>
          )}

          {/* Assignments Step */}
          {currentStep === "assignments" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-foreground mb-2">Class Assignments</h3>
                <p className="text-foreground/60">
                  Assign classes and subjects to this teacher. You can add assignments later.
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-foreground/70">
                  Class assignments can be configured after the teacher is created through the staff management interface.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center mt-6 pt-4 border-t border-stroke">
          <button
            type="button"
            onClick={handlePrevious}
            disabled={currentStep === "role_selection" || (currentStep === "personal_info" && initialData)}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft size={16} />
            <span>Previous</span>
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleNext}
              disabled={!isStepValid() || isSubmitting}
              className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting && <CircularLoader size={16} color="white" />}
              <span>
                {currentStep === "assignments" || (currentStep === "permissions" && formData.role_template !== "teacher") 
                  ? (initialData ? "Update" : "Create") 
                  : "Next"}
              </span>
              {!isSubmitting && (currentStep !== "assignments" && !(currentStep === "permissions" && formData.role_template !== "teacher")) && (
                <ChevronRight size={16} />
              )}
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  );
}
