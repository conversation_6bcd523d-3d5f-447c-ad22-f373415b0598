"use client";

import { Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import React, { Suspense, useEffect, useState } from "react";
import useAuth from "@/app/hooks/useAuth";
import { SchoolAdminDashboardSkeleton } from "@/components/skeletons";
import PageWrapper from "@/components/Dashboard/ReusableComponents/ClassComponent";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Presentation,
  baseHref: `${BASE_URL}/classes`,
  title: "Classes"
};

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  return (
    <Suspense fallback={
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <SchoolAdminDashboardSkeleton />
      </SchoolLayout>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <PageWrapper user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
