"use client";

import { FileText } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { ExamTypeSchema } from "@/app/models/ExampType";
import { getExamTypes, createExamType, updateExamType, deleteExamType, deleteMultipleExamTypes } from "@/app/services/ExamTypeServices";
import ExamTypeModal from "@/components/modals/ExamTypeModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import ExamTypeSkeleton from "@/components/skeletons/ExamTypeSkeleton";
import { verifyPassword } from "@/app/services/UserServices";

const BASE_URL = "/school-admin";

const navigation = {
  icon: FileText,
  baseHref: `${BASE_URL}/examtype`,
  title: "Exam Types"
};

function ExamTypeContent() {
  const [examTypes, setExamTypes] = useState<ExamTypeSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedExamTypes, setSelectedExamTypes] = useState<ExamTypeSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [examTypeToEdit, setExamTypeToEdit] = useState<ExamTypeSchema | null>(null);
  const [examTypeToDelete, setExamTypeToDelete] = useState<ExamTypeSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const { user } = useAuth();
  const router = useRouter();

  // Columns for the table
  const columns = [
    {
      header: "Exam Type",
      accessor: (row: ExamTypeSchema) => (
        <span className="font-medium">{row.type}</span>
      )
    },
    {
      header: "Created At",
      accessor: (row: ExamTypeSchema) => {
        if (!row.createdAt) return "N/A";
        try {
          return new Date(row.createdAt).toLocaleDateString();
        } catch {
          return "Invalid Date";
        }
      }
    }
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (examType: ExamTypeSchema) => {
        handleEditExamType(examType);
      },
    },
    {
      label: "Delete",
      onClick: (examType: ExamTypeSchema) => {
        handleDeleteExamType(examType);
      },
    },
  ];

  // Load exam types on page load
  useEffect(() => {
    fetchExamTypes();
  }, []);

  const fetchExamTypes = async () => {
    try {
      setLoadingData(true);
      const examTypesData = await getExamTypes();
      // Filter by school if user has school_ids
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const filteredExamTypes = examTypesData.filter(examType => examType.school_id === schoolId);
        setExamTypes(filteredExamTypes);
      } else {
        setExamTypes(examTypesData);
      }
    } catch (error) {
      console.error("Error fetching exam types:", error);
      setSubmitStatus(createErrorNotification("Failed to fetch exam types"));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new exam type
  const handleCreateExamType = () => {
    setExamTypeToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing exam type
  const handleEditExamType = (examType: ExamTypeSchema) => {
    setExamTypeToEdit(examType);
    setIsModalOpen(true);
  };

  // Handle deleting single exam type
  const handleDeleteExamType = (examType: ExamTypeSchema) => {
    setExamTypeToDelete(examType);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple exam types
  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setExamTypeToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle selection change
  const handleSelectionChange = (selected: ExamTypeSchema[]) => {
    setSelectedExamTypes(selected);
  };

  // Handle save (create or update)
  const handleSave = async (examTypeData: any) => {
    setIsSubmitting(true);
    try {
      if (examTypeToEdit) {
        // Update existing exam type
        await updateExamType(examTypeToEdit._id, examTypeData);
        setSubmitStatus(createSuccessNotification("Exam type updated successfully"));
      } else {
        // Create new exam type
        await createExamType(examTypeData);
        setSubmitStatus(createSuccessNotification("Exam type created successfully"));
      }
      
      setIsModalOpen(false);
      setExamTypeToEdit(null);
      await fetchExamTypes(); // Refresh the list
    } catch (error) {
      console.error("Error saving exam type:", error);
      setSubmitStatus(createErrorNotification("Failed to save exam type"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation with password
  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        throw new Error("User email not found");
      }

      await verifyPassword(user.email, password);

      if (deleteType === "single" && examTypeToDelete) {
        // Delete single exam type
        await deleteExamType(examTypeToDelete._id);
        setSubmitStatus(createSuccessNotification("Exam type deleted successfully"));
      } else if (deleteType === "multiple" && selectedExamTypes.length > 0) {
        // Delete multiple exam types
        const ids = selectedExamTypes.map(examType => examType._id);
        await deleteMultipleExamTypes(ids);
        setSubmitStatus(createSuccessNotification(`${selectedExamTypes.length} exam types deleted successfully`));
        setSelectedExamTypes([]);
        setClearSelection(true);
      }

      setIsDeleteModalOpen(false);
      setExamTypeToDelete(null);
      await fetchExamTypes(); // Refresh the list
    } catch (error) {
      console.error("Error deleting exam type(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification("Invalid password"));
      } else {
        setSubmitStatus(createErrorNotification("Failed to delete exam type(s)"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <ExamTypeSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">Exam Types Management</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateExamType}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        Add New Exam Type
      </motion.button>

      <DataTableFix<ExamTypeSchema>
        data={examTypes}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      {/* Exam Type Modal */}
      <ExamTypeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setExamTypeToEdit(null);
        }}
        onSave={handleSave}
        examType={examTypeToEdit}
        isSubmitting={isSubmitting}
      />

      {/* Delete Confirmation Modal with Password */}
      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setExamTypeToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={
          deleteType === "single"
            ? "Delete Exam Type"
            : "Delete Selected Exam Types"
        }
        message={
          deleteType === "single"
            ? "Are you sure you want to delete this exam type? This action cannot be undone."
            : `Are you sure you want to delete ${selectedExamTypes.length} selected exam types? This action cannot be undone.`
        }
        itemName={
          deleteType === "single" && examTypeToDelete
            ? examTypeToDelete.type
            : undefined
        }
        itemCount={deleteType === "multiple" ? selectedExamTypes.length : undefined}
        type={deleteType}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <ExamTypeContent />
      </SchoolLayout>
    </Suspense>
  );
}
