import { ChatbotContext } from '@/hooks/useChatbotContext';

export interface ChatbotMessage {
  message: string;
  context: ChatbotContext;
  timestamp: Date;
}

export interface ChatbotResponse {
  response: string;
  actions?: ChatbotAction[];
  suggestions?: string[];
  metadata?: {
    confidence: number;
    processing_time: number;
    workflow_used: string;
    intent?: string;
  };
}

export interface ChatbotAction {
  type: 'navigate' | 'open_modal' | 'execute_function' | 'show_data';
  target: string;
  label?: string;
  params?: Record<string, any>;
}

class ChatbotService {
  private n8nWebhookUrl: string;
  private fallbackResponses: Record<string, ChatbotResponse>;

  constructor() {
    this.n8nWebhookUrl = process.env.NEXT_PUBLIC_N8N_WEBHOOK_URL || 'http://localhost:5678/webhook/chatbot';
    
    // Fallback responses for when n8n is unavailable
    this.fallbackResponses = {
      'super-admin': {
        response: "I'm temporarily unavailable. As Super Admin, you can access all features via the navigation menu.",
        suggestions: ["Go to schools", "Manage users", "View dashboard", "Check subscriptions"]
      },
      'school-admin': {
        response: "Service temporarily unavailable. You can use the menu to manage your school.",
        suggestions: ["Manage classes", "View students", "Add teacher", "Generate report"]
      },
      'teacher': {
        response: "Assistant temporarily unavailable. Use the menu to access your teaching features.",
        suggestions: ["My classes", "Mark attendance", "Add grades", "View schedule"]
      },
      'counselor': {
        response: "Service temporarily unavailable. You can use the menu for your counseling activities.",
        suggestions: ["Enroll student", "View records", "Schedule appointment", "Reports"]
      },
      'parent': {
        response: "Service temporarily unavailable. You can use the menu to view your child's information.",
        suggestions: ["View child progress", "Contact teachers", "View assignments", "Pay fees"]
      }
    };
  }

  async sendMessage(message: string, context: ChatbotContext): Promise<ChatbotResponse> {
    try {
      const payload: ChatbotMessage = {
        message,
        context,
        timestamp: new Date()
      };

      console.log('🚀 Sending message to n8n:', payload);

      const response = await fetch(this.n8nWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
          'X-Dashboard-Type': context.dashboard.type,
          'X-User-Role': context.user.role
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Received response from n8n:', data);
      
      return data as ChatbotResponse;
    } catch (error) {
      console.error('❌ Chatbot service error:', error);
      
      // Return fallback response based on dashboard type
      const fallback = this.fallbackResponses[context.dashboard.type] || this.fallbackResponses['super-admin'];
      
      return {
        ...fallback,
        metadata: {
          confidence: 0.1,
          processing_time: 0,
          workflow_used: 'fallback',
          intent: 'error_fallback'
        }
      };
    }
  }

  private getAuthToken(): string {
    // Get token from localStorage or auth context
    if (typeof window !== 'undefined') {
      return localStorage.getItem('idToken') || '';
    }
    return '';
  }

  // Method to test n8n connectivity
  async testConnection(): Promise<boolean> {
    try {
      const testPayload = {
        message: "test_connection",
        context: {
          user: { role: 'super' },
          dashboard: { type: 'super-admin' }
        },
        timestamp: new Date()
      };

      const response = await fetch(this.n8nWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(testPayload)
      });

      return response.ok;
    } catch (error) {
      console.error('n8n connection test failed:', error);
      return false;
    }
  }

  // Method to get dashboard-specific suggestions
  getDashboardSuggestions(dashboardType: string): string[] {
    const suggestions: Record<string, string[]> = {
      'super-admin': [
        "Create a new school",
        "View global statistics",
        "Manage users",
        "Check subscriptions",
        "System settings",
        "Generate global report"
      ],
      'school-admin': [
        "Add a new class",
        "View unpaid students",
        "Create teacher account",
        "Generate school report",
        "Manage school fees",
        "View school analytics"
      ],
      'teacher': [
        "Mark attendance",
        "Add grades",
        "View my schedule",
        "Contact parents",
        "Create lesson plan",
        "View student progress"
      ],
      'counselor': [
        "Enroll new student",
        "View student record",
        "Schedule appointment",
        "Add counseling note",
        "Track academic progress",
        "Generate student report"
      ],
      'parent': [
        "View my child's progress",
        "Contact teachers",
        "View assignments",
        "Check attendance",
        "Pay school fees",
        "Schedule parent meeting"
      ]
    };

    return suggestions[dashboardType] || suggestions['super-admin'];
  }
}

export const chatbotService = new ChatbotService();
