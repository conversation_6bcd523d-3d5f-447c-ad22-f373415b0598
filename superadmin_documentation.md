# Documentation Complète - Super Admin Dashboard Scholarify

## Vue d'ensemble du système

Scholarify est une plateforme complète de gestion scolaire qui permet aux établissements d'enseignement de gérer efficacement leurs opérations quotidiennes. Le Super Admin Dashboard constitue le niveau d'administration le plus élevé du système, offrant un contrôle total sur toutes les fonctionnalités et données de la plateforme.

## Architecture du système

### Stack technologique
- **Frontend** : Next.js 14 avec TypeScript
- **Backend** : Node.js avec Express.js
- **Base de données** : MongoDB avec Mongoose ODM
- **Authentification** : Firebase Authentication
- **Stockage** : Cloudinary pour les médias
- **Paiements** : Intégration CinetPay et Fapshi
- **Notifications** : Service SMS et Email intégré

### Structure des rôles
1. **Super Admin** (`super`) - Accès complet à toutes les fonctionnalités
2. **School Admin** (`admin`) - Gestion d'une école spécifique
3. **Teacher** (`teacher`) - Fonctionnalités pédagogiques
4. **Counselor** (`counselor`) - Enregistrement et suivi des étudiants
5. **Parent** (`parent`) - Suivi des enfants et communication

## Fonctionnalités du Super Admin Dashboard

### 1. Dashboard Principal (`/super-admin/dashboard`)

#### Métriques globales affichées
- **Total Revenue (USD)** : Revenus totaux de toutes les écoles
- **Total Schools** : Nombre total d'écoles enregistrées
- **Total Students** : Nombre total d'étudiants sur la plateforme
- **Total Users** : Nombre total d'utilisateurs actifs

#### Graphiques et analyses
- **Graphique de performance** : Évolution des métriques clés dans le temps
- **Tableau de performance** : Données détaillées par école avec métriques personnalisables
- **Analyses comparatives** : Comparaison des performances entre écoles

### 2. Gestion des écoles (`/super-admin/schools`)

#### Fonctionnalités disponibles
- **Création d'écoles** : Formulaire complet avec informations détaillées
- **Modification d'écoles** : Mise à jour des informations existantes
- **Suppression d'écoles** : Suppression avec confirmation et sauvegarde
- **Visualisation** : Liste complète avec filtres et recherche
- **Statistiques par école** : Métriques individuelles détaillées

#### Données gérées
```typescript
interface School {
  _id: string;
  school_id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  principal_name: string;
  principal_contact: string;
  establishment_date: Date;
  school_type: 'public' | 'private' | 'international';
  academic_levels: string[];
  total_students: number;
  total_teachers: number;
  status: 'active' | 'inactive' | 'suspended';
  subscription_plan: string;
  created_at: Date;
  updated_at: Date;
}
```

### 3. Gestion des utilisateurs (`/super-admin/users`)

#### Types d'utilisateurs gérés
- **Administrateurs d'école** : Création et gestion des comptes admin
- **Professeurs** : Gestion des comptes enseignants
- **Conseillers** : Gestion des comptes counselors
- **Parents** : Supervision des comptes parents

#### Fonctionnalités de gestion
- **Création d'utilisateurs** : Formulaires spécialisés par rôle
- **Attribution de rôles** : Gestion des permissions et accès
- **Gestion des écoles** : Attribution d'écoles aux utilisateurs
- **Statut des comptes** : Activation/désactivation des comptes
- **Réinitialisation de mots de passe** : Gestion des accès

#### Modèle utilisateur
```typescript
interface User {
  _id: string;
  user_id: string;
  firebaseUid: string;
  name: string;
  role: 'admin' | 'teacher' | 'parent' | 'super';
  email: string;
  phone: string;
  avatar?: string;
  address?: string;
  school_ids: string[];
  student_ids: string[];
  isVerified: boolean;
  lastLogin: Date;
  created_at: Date;
  updated_at: Date;
}
```

### 4. Gestion des subscriptions (`/super-admin/subscription`)

#### Fonctionnalités de subscription
- **Plans d'abonnement** : Gestion des différents plans tarifaires
- **Suivi des paiements** : Historique des transactions
- **Renouvellements** : Gestion des échéances et renouvellements
- **Statistiques financières** : Revenus par période et par école

#### Modèle de subscription
```typescript
interface Subscription {
  _id: string;
  subscription_id: string;
  transaction_id: string;
  guardian_id: string;
  student_id: string[];
  amount: number;
  email: string;
  status: boolean;
  expiryDate: Date;
  created_at: Date;
  updated_at: Date;
}
```

### 5. Gestion globale des classes (`/super-admin/classes`)

#### Vue d'ensemble des classes
- **Classes par école** : Visualisation hiérarchique
- **Niveaux académiques** : Gestion des niveaux (CP1, CP2, CE1, etc.)
- **Capacités des classes** : Suivi des effectifs
- **Professeurs assignés** : Attribution des enseignants

#### Modèle de classe
```typescript
interface Class {
  _id: string;
  class_id: string;
  name: string;
  level: string;
  school_id: string;
  teacher_id?: string;
  academic_year: string;
  capacity: number;
  current_enrollment: number;
  subjects: string[];
  schedule: ClassSchedule[];
  created_at: Date;
  updated_at: Date;
}
```

### 6. Gestion globale des étudiants (`/super-admin/students`)

#### Fonctionnalités étudiants
- **Vue globale** : Tous les étudiants de toutes les écoles
- **Recherche avancée** : Filtres par école, classe, statut
- **Statistiques démographiques** : Analyses par âge, genre, région
- **Suivi académique** : Performances globales

#### Modèle étudiant
```typescript
interface Student {
  _id: string;
  student_id: string;
  name: string;
  date_of_birth: Date;
  gender: 'male' | 'female';
  address: string;
  phone?: string;
  email?: string;
  school_id: string;
  class_id: string;
  parent_ids: string[];
  academic_year: string;
  enrollment_date: Date;
  status: 'active' | 'inactive' | 'graduated' | 'transferred';
  medical_info?: string;
  emergency_contact: {
    name: string;
    phone: string;
    relationship: string;
  };
  created_at: Date;
  updated_at: Date;
}
```

### 7. Invitation des parents (`/super-admin/parents`)

#### Système d'invitation
- **Génération d'invitations** : Création de liens d'invitation sécurisés
- **Suivi des invitations** : Statut des invitations envoyées
- **Gestion des codes** : Codes d'invitation temporaires
- **Communication** : Envoi automatique par SMS/Email

#### Modèle d'invitation
```typescript
interface Invitation {
  _id: string;
  invitation_id: string;
  student_id: string;
  parent_email?: string;
  parent_phone?: string;
  invitation_code: string;
  status: 'pending' | 'accepted' | 'expired';
  expires_at: Date;
  sent_at: Date;
  accepted_at?: Date;
  created_by: string;
  created_at: Date;
}
```

## APIs et services backend

### Structure des routes principales
```javascript
// Routes disponibles pour le Super Admin
router.use('/user', userRoutes);           // Gestion des utilisateurs
router.use('/school', schoolRoutes);       // Gestion des écoles
router.use('/class', classRoutes);         // Gestion des classes
router.use('/subject', subjectRoutes);     // Gestion des matières
router.use('/attendance', attendanceRoutes); // Présences
router.use('/grade', gradeRoutes);         // Notes et évaluations
router.use('/student', studentRoutes);     // Gestion des étudiants
router.use('/resources', resourcesRoutes); // Ressources pédagogiques
router.use('/discipline', disciplineRoutes); // Discipline
router.use('/announcement', announcementRoutes); // Annonces
router.use('/subscription', subscriptionRoutes); // Subscriptions
router.use('/auth', authRoutes);           // Authentification
router.use('/payment', paymentRoutes);     // Paiements
router.use('/periods', periodRoutes);      // Périodes scolaires
router.use('/schedule', classScheduleRoutes); // Emplois du temps
router.use('/exam', examTypeRoutes);       // Types d'examens
router.use('/class-level', classLevelRoutes); // Niveaux de classe
router.use('/invitation', invitationRoutes); // Invitations
router.use('/academic-years', academicYearRoutes); // Années académiques
router.use('/school-resources', schoolResourcesRoutes); // Ressources d'école
router.use('/fees', FeesRoutes);           // Frais scolaires
router.use('/fee-payment', FeePayment);    // Paiements de frais
router.use('/settings', SettingsRoutes);   // Paramètres système
router.use('/credit', CreditRoutes);       // Système de crédits
```

### Services intégrés

#### Service d'authentification
- **Firebase Authentication** : Gestion sécurisée des comptes
- **Middleware de vérification** : Contrôle d'accès par rôle
- **Tokens JWT** : Sessions sécurisées
- **Réinitialisation de mots de passe** : Processus automatisé

#### Service de paiement
- **CinetPay** : Paiements mobiles en Afrique
- **Fapshi** : Alternative de paiement
- **Suivi des transactions** : Historique complet
- **Webhooks** : Notifications automatiques

#### Service de communication
- **SMS** : Notifications par SMS
- **Email** : Communications par email
- **Templates** : Modèles de messages personnalisés
- **Logs** : Suivi des communications envoyées

#### Service de stockage
- **Cloudinary** : Stockage et optimisation d'images
- **Upload sécurisé** : Validation des fichiers
- **Transformations** : Redimensionnement automatique
- **CDN** : Distribution rapide des contenus

## Sécurité et permissions

### Système de permissions
Le Super Admin a accès à toutes les fonctionnalités sans restriction, mais le système maintient une traçabilité complète des actions :

#### Logs d'audit
- **Actions utilisateur** : Toutes les actions sont enregistrées
- **Modifications de données** : Historique des changements
- **Accès aux données** : Suivi des consultations
- **Erreurs système** : Logs d'erreurs détaillés

#### Sécurité des données
- **Chiffrement** : Données sensibles chiffrées
- **Validation** : Validation stricte des entrées
- **Sanitisation** : Nettoyage des données utilisateur
- **Rate limiting** : Protection contre les abus

### Contrôle d'accès
```typescript
// Middleware de vérification des permissions
const checkSuperAdminAccess = (req, res, next) => {
  if (req.user.role !== 'super') {
    return res.status(403).json({
      error: 'Accès refusé - Privilèges Super Admin requis'
    });
  }
  next();
};
```

## Intégrations et workflows

### Intégration Firebase
- **Authentication** : Gestion des comptes utilisateur
- **Realtime Database** : Synchronisation en temps réel
- **Cloud Functions** : Logique métier côté serveur
- **Analytics** : Suivi d'utilisation

### Workflows automatisés
- **Création d'année académique** : Processus automatisé annuel
- **Mise à jour des âges** : Calcul automatique des âges étudiants
- **Notifications d'échéance** : Rappels de paiement automatiques
- **Rapports périodiques** : Génération automatique de rapports

### Jobs programmés (Cron Jobs)
```javascript
// Exemples de tâches automatisées
- Mise à jour quotidienne des âges des étudiants
- Vérification hebdomadaire des subscriptions expirées
- Génération mensuelle des rapports financiers
- Nettoyage trimestriel des données temporaires
```

## Interface utilisateur et expérience

### Design et ergonomie
- **Interface responsive** : Adaptation mobile et desktop
- **Thème sombre/clair** : Personnalisation de l'affichage
- **Navigation intuitive** : Menu latéral avec breadcrumbs
- **Recherche globale** : Recherche rapide dans toutes les données

### Composants réutilisables
- **StatsOverview** : Cartes de statistiques avec indicateurs
- **DataTable** : Tableaux avec tri, filtres et pagination
- **Modal** : Fenêtres modales pour les actions
- **Forms** : Formulaires avec validation
- **Charts** : Graphiques interactifs

### Notifications et feedback
- **Notifications toast** : Feedback immédiat des actions
- **Confirmations** : Dialogues de confirmation pour actions critiques
- **Loading states** : Indicateurs de chargement
- **Error handling** : Gestion gracieuse des erreurs

## Monitoring et analytics

### Métriques système
- **Performance** : Temps de réponse des APIs
- **Utilisation** : Statistiques d'usage par fonctionnalité
- **Erreurs** : Taux d'erreur et types d'erreurs
- **Capacité** : Utilisation des ressources serveur

### Métriques métier
- **Croissance** : Évolution du nombre d'écoles et d'utilisateurs
- **Engagement** : Fréquence d'utilisation par rôle
- **Revenus** : Suivi des revenus et conversions
- **Satisfaction** : Métriques de satisfaction utilisateur

### Rapports automatisés
- **Rapports quotidiens** : Activité et performance
- **Rapports hebdomadaires** : Tendances et analyses
- **Rapports mensuels** : Bilan complet et recommandations
- **Rapports annuels** : Vue d'ensemble et planification

## Maintenance et support

### Outils de maintenance
- **Base de données** : Outils de sauvegarde et restauration
- **Logs** : Centralisation et analyse des logs
- **Monitoring** : Surveillance en temps réel
- **Alertes** : Notifications automatiques des problèmes

### Support utilisateur
- **Documentation** : Guides utilisateur détaillés
- **Formation** : Matériel de formation par rôle
- **Support technique** : Système de tickets
- **FAQ** : Base de connaissances

### Évolutions et mises à jour
- **Versioning** : Gestion des versions de l'application
- **Déploiement** : Processus de mise en production
- **Tests** : Suites de tests automatisés
- **Rollback** : Procédures de retour en arrière

## Modèles de données détaillés

### Modèle Academic Year
```typescript
interface AcademicYear {
  _id: string;
  year_id: string;
  name: string; // "2023-2024"
  start_date: Date;
  end_date: Date;
  is_current: boolean;
  school_id: string;
  periods: Period[];
  created_at: Date;
  updated_at: Date;
}
```

### Modèle Attendance
```typescript
interface Attendance {
  _id: string;
  attendance_id: string;
  student_id: string;
  class_id: string;
  date: Date;
  status: 'present' | 'absent' | 'late' | 'excused';
  marked_by: string; // teacher_id
  notes?: string;
  created_at: Date;
  updated_at: Date;
}
```

### Modèle Grade
```typescript
interface Grade {
  _id: string;
  grade_id: string;
  student_id: string;
  subject_id: string;
  class_id: string;
  exam_type: string;
  score: number;
  max_score: number;
  percentage: number;
  grade_letter: string; // A, B, C, D, F
  academic_year: string;
  period: string;
  teacher_id: string;
  date_recorded: Date;
  comments?: string;
  created_at: Date;
  updated_at: Date;
}
```

### Modèle Fees
```typescript
interface Fees {
  _id: string;
  fee_id: string;
  student_id: string;
  school_id: string;
  academic_year: string;
  fee_type: 'tuition' | 'registration' | 'transport' | 'meals' | 'other';
  amount: number;
  due_date: Date;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  payment_method?: string;
  transaction_id?: string;
  paid_date?: Date;
  created_at: Date;
  updated_at: Date;
}
```

### Modèle Resources
```typescript
interface Resources {
  _id: string;
  resource_id: string;
  title: string;
  description: string;
  type: 'document' | 'video' | 'audio' | 'image' | 'link';
  file_url?: string;
  file_size?: number;
  subject_id?: string;
  class_level?: string;
  uploaded_by: string;
  is_public: boolean;
  download_count: number;
  tags: string[];
  created_at: Date;
  updated_at: Date;
}
```

## Fonctionnalités avancées

### Système de rapports
- **Rapports financiers** : Revenus, dépenses, bénéfices par école
- **Rapports académiques** : Performances des étudiants, statistiques de classe
- **Rapports d'utilisation** : Activité des utilisateurs, fonctionnalités populaires
- **Rapports personnalisés** : Création de rapports sur mesure

### Analytics avancées
- **Tableaux de bord interactifs** : Visualisations dynamiques
- **Prédictions** : Modèles prédictifs pour les performances
- **Comparaisons** : Analyses comparatives entre écoles
- **Tendances** : Identification des tendances à long terme

### Outils d'administration
- **Gestion des sauvegardes** : Planification et restauration
- **Migration de données** : Outils d'import/export
- **Maintenance système** : Nettoyage et optimisation
- **Configuration globale** : Paramètres système centralisés

## Conclusion

Le Super Admin Dashboard de Scholarify constitue le centre de contrôle principal de la plateforme, offrant une vue d'ensemble complète et des outils de gestion avancés pour administrer efficacement l'ensemble du système éducatif. Avec ses fonctionnalités étendues, son architecture robuste et ses intégrations multiples, il permet une gestion centralisée et efficace de tous les aspects de la plateforme éducative.

Cette documentation complète sert de référence pour comprendre l'ensemble des capacités du système et peut être utilisée pour former les utilisateurs, développer de nouvelles fonctionnalités, ou intégrer des systèmes tiers comme le chatbot IA.
